
import React from "react";
import {
	cdnUrlMock,
	apiUrlMock,
	playerUrlMock
} from "./src/tests/mocks/data.mock";

jest.mock("@player/assets/icon-arrow.svg", () => "icon-arrow");
jest.mock("@player/assets/icon-volume-off.svg", () => "icon-volume-off");
jest.mock("@player/assets/icon-volume-on.svg", () => "icon-volume-on");
jest.mock("@player/assets/icon-heart-fill-red.svg", () => "icon-heart-fill-red");
jest.mock("@player/assets/icon-heart-outline.svg", () => "icon-heart-outline");
jest.mock("@player/assets/icon-play.svg", () => "con-play");
jest.mock("@player/assets/icon-shop.svg", () => "icon-shop");
jest.mock("@player/assets/icon-times.svg", () => "icon-times");
jest.mock("@player/assets/icon-cc-fill.svg", () => "icon-cc-fill");
jest.mock("@player/assets/icon-cc-outline.svg", () => "icon-cc-outline");
jest.mock("@player/assets/icon-email.svg", () => "icon-email");
jest.mock("@player/assets/icon-phone.svg", () => "icon-phone");
jest.mock("@player/assets/icon-full-screen.svg", () => "icon-full-screen");
jest.mock("@player/assets/icon-exit-full-screen.svg", () => "icon-exit-full-screen");
jest.mock("@player/assets/icon-pause.svg", () => "icon-pause");


interface LottiePlayerProps {
	play?: boolean;
	loop?: boolean;
	animationData?: object;
	onComplete?: () => void;
	style?: React.CSSProperties;
}
jest.mock("react-lottie-player", () => {
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const LottiePlayer: React.FC<LottiePlayerProps> = ({ play, loop, animationData, onComplete, style }) =>
		(<div style={style} data-testid="mock-lottie">Mock Lottie Animation</div>);

	LottiePlayer.displayName = "LottiePlayer";
	return LottiePlayer;
});


const originalEnv = { ...process.env };
beforeAll(() => {
	process.env.GP_CDN_ENDPOINT = cdnUrlMock;
	process.env.GP_SERVER_API_ENDPOINT = apiUrlMock;
	process.env.GP_PLAYER_ENDPOINT = playerUrlMock;

});
afterAll(() => {
	process.env = { ...originalEnv };
});
