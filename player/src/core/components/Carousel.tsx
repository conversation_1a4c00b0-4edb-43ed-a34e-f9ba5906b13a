import React, {
	useRef,
	useEffect,
	useState,
	useCallback
} from "react";
import styled from "styled-components/macro";
import {
	Flex,
	Box
} from "@player/components";
import { IconButton } from "@player/components/button/IconButton";
import {
	EventNameEnum,
	sendAppEvent
} from "@player/event";
import {
	SnippetTypeEnum,
	VideoDisplayModeEnum,
	IVideoData
} from "@player/app/app.interface";
import { default as PlayIcon } from "@player/assets/icon-play.svg";
import { default as ShopIcon } from "@player/assets/icon-shop.svg";
import { ITheme } from "@player/theme/theme.interface";
import { BlurBox } from "@player/components/Box";

const GPCarousel = styled(Box)`
    .gp-carousel-con {
		display: flex;
		flex-wrap: nowrap;
		white-space: nowrap;
        overflow-x: auto;
        overflow-y: hidden;
        gap: ${(props):string => props.theme.carouselGap + "px"};
		padding-left: ${(props):string => props.theme.carouselMargin + "px"};
		padding-right: ${(props):string => props.theme.carouselMargin + "px"};
        &::-webkit-scrollbar {
            display: none;
        }
    }

	${(props): string => props.theme.carouselIsCentered && `
        .gp-carousel-con .carousel-wrapper:first-child {
            margin-left: auto;
        }
        .gp-carousel-con .carousel-wrapper:last-child {
            margin-right: auto;
        }
    `}

    .gp-carousel-con .carousel-wrapper {
        cursor: pointer;
        border-radius: ${(props):string => props.theme.carouselBorderRadius + "px"};
        z-index: 1;
    }
    .gp-carousel-con .gp-poster-con {
        flex-direction: column;
        height: 100%;
        padding: 3px;
        justify-content: space-between;
    }
`;

const VideoTag = styled.video`
    width: 100%;
    height: 100%;
    border-radius: ${(props):string => props.theme.carouselBorderRadius + "px"};
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
`;

const CarouselContainer = styled(Flex)`
    position: relative;
`;

const VideoPlayContainer = styled(Flex)`
    position: absolute;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    bottom: 0;
    z-index: -2;
    cursor: pointer;
`;

const Wrapper = styled(Box).attrs<{posterURL: string, isLandscape: boolean}>(({ posterURL }) => ({
	style: {
		backgroundImage: `url("${posterURL}")`,
		backgroundSize: "cover",
		backgroundPosition: "center"
	}
}))<{posterURL: string, isLandscape: boolean}>`
	position: relative;
	flex: 0 0 auto;
	max-width: ${(props): string => (props.isLandscape ? "630px" : "261px")};
	min-width: ${(props): string => (props.isLandscape ? "630px" : "261px")};
	aspect-ratio: ${(props): string => (props.isLandscape ? "14/9" : "9/14")};
	@media (max-width: 768px) {
		max-width: ${(props): string => (props.isLandscape ? "90vw" : "37vw")};
		min-width: ${(props): string => (props.isLandscape ? "90vw" : "37vw")};
	}
`;


const PosterContainer = styled(Flex)``;

const AnimatedCTA = styled(Flex).attrs<{isHovered: boolean}>(
	({ isHovered }) => ({
		style: {
			position: "relative",
			alignItems: "center",
			opacity: isHovered ? 1 : 0,
			transform: isHovered ? "translateY(0)" : "translateY(100%)",
			transition: "opacity 0.5s, transform 0.5s",
			borderRadius: "10px"
		}
	})
)<{isHovered: boolean}>``;

const CTAText = styled.h3.attrs<{ theme: ITheme }>(
	({ theme }) => ({
		style: {
			margin: "0px",
			padding: "0px",
			color: theme.textColor,
			fontFamily: theme.fonts.family,
			fontWeight: 600,
			fontSize: "15px",
			lineHeight: "20px",
			zIndex: 1,
			whiteSpace: "normal",
			wordWrap: "break-word",
			overflowWrap: "break-word",
			maxWidth: "100%"
		}
	}))<{ theme: ITheme }>``;


interface Props {
	videoData: IVideoData[];
	handleCTAClick: (videoId: string) => void;
	collectionId: string;
}

const isLargeScreen = window.innerWidth > 560;

export const Carousel: React.FC<Props> = ({
	videoData,
	handleCTAClick,
	collectionId
}) => {
	const videoRefs = useRef<(HTMLVideoElement | null)[]>([]);
	const [wasViewable, setWasViewable] = useState<boolean>(false);
	const carouselContainerRef = useRef<HTMLDivElement | null>(null);
	const [currentPlayingVideo, setCurrentPlayingVideo] =
        useState<HTMLVideoElement | null>(null);
	const [isHovered, setIsHovered] = useState<string | null>(null);

	videoRefs.current = videoData.map(
		(_, i) => videoRefs.current[i] ?? null
	);

	const sendViewableEvent = useCallback(() => {
		if (wasViewable) return;
		sendAppEvent({
			eventName: EventNameEnum.SNIPPET_VIEWABLE_IMPRESSION,
			accountId: videoData[0].accountId,
			collectionId,
			snippet: { type: SnippetTypeEnum.CAROUSEL }
		});
		setWasViewable(true);
	}, [wasViewable, videoData, collectionId]);

	const loadVideoUrl = useCallback(
		(videoEl: HTMLVideoElement, videoURL: string) => {
			videoEl.src = videoURL;

			const playVideo = (): void => {
				videoEl
					.play()
					.catch((err) =>
						console.error(
							"loadVideoUrl > play error: ",
							videoURL,
							err
						)
					);
				videoEl.removeEventListener("loadedmetadata", playVideo);
			};

			videoEl.addEventListener("loadedmetadata", playVideo);
			videoEl.load();
		},
		[]
	);

	const unloadVideoUrl = useCallback((videoEl: HTMLVideoElement) => {
		videoEl.pause();
		videoEl.removeAttribute("src");
		videoEl.load();
	}, []);

	useEffect(() => {
		if (videoRefs.current[0]) {
			videoRefs.current[0].src = videoData[0].videoURL;
			videoRefs.current[0].load();
			setCurrentPlayingVideo(videoRefs.current[0]);
		}
	}, [videoData]);

	const handleMouseEnter = (videoEl: HTMLVideoElement, videoData: IVideoData): void => {
		if (!isLargeScreen) return;
		setIsHovered(videoData._id);
		if (videoEl === currentPlayingVideo) return;

		videoRefs.current.forEach((v) => {
			if (v && v !== videoEl) {
				unloadVideoUrl(v);
			}
		});

		loadVideoUrl(videoEl, videoData.videoURL);
		setCurrentPlayingVideo(videoEl);
		sendAppEvent({
			eventName: EventNameEnum.HOVER_PLAY_START,
			accountId: videoData.accountId,
			collectionId,
			videoId: videoData._id,
			snippet: { type: SnippetTypeEnum.CAROUSEL }
		});
	};

	const checkVisibility = useCallback(
		(entries: IntersectionObserverEntry[]) => {
			if (isLargeScreen) {
				entries.forEach((entry) => {
					if (entry.isIntersecting && videoRefs.current[0] && currentPlayingVideo) {
						currentPlayingVideo.play().catch((err) =>console.error("checkVisibility > play error:", err));
						sendViewableEvent();
					}
					if (
						entry.target.className.includes("gp-carousel-con") &&
                        currentPlayingVideo
					) {
						if (entry.isIntersecting) {
							currentPlayingVideo
								.play()
								.catch((err) =>
									console.error(
										"checkVisibility > play error:",
										err
									)
								);
							sendViewableEvent();
						} else {
							currentPlayingVideo.pause();
						}
					}
				});
			} else {
				entries.forEach((entry) => {
					const videoEl = entry.target as HTMLVideoElement;
					if (!videoEl) return;

					if (entry.isIntersecting) {
						if (currentPlayingVideo) {
							unloadVideoUrl(currentPlayingVideo);
						}

						loadVideoUrl(
							videoEl,
							videoEl.getAttribute("data-src") || ""
						);
						setCurrentPlayingVideo(videoEl);
						sendViewableEvent();
					} else if (!entry.isIntersecting) {
						unloadVideoUrl(videoEl);
						setCurrentPlayingVideo(null);
					}
				});
			}
		},
		[currentPlayingVideo, loadVideoUrl, sendViewableEvent, unloadVideoUrl]
	);

	useEffect(() => {
		const observer = new IntersectionObserver(checkVisibility, {
			root: null,
			threshold: 0.9
		});
		const currentCarouselContainer = carouselContainerRef.current;

		if (isLargeScreen) {
			if (currentCarouselContainer) {
				observer.observe(currentCarouselContainer);
			}
			if (videoRefs.current[0]) {
				observer.observe(videoRefs.current[0]);
			}
		} else {
			videoRefs.current.forEach((videoRef) => {
				if (videoRef) {
					observer.observe(videoRef);
				}
			});
		}

		return () => {
			if (isLargeScreen) {
				if (currentCarouselContainer) {
					observer.unobserve(currentCarouselContainer);
				}
				if (videoRefs.current[0]) {
					observer.unobserve(videoRefs.current[0]);
				}
			} else {
				videoRefs.current.forEach((videoRef) => {
					if (videoRef) {
						observer.unobserve(videoRef);
					}
				});
			}
		};
	}, [checkVisibility]);

	return (
		<GPCarousel>
			<CarouselContainer
				ref={carouselContainerRef}
				className="gp-carousel-con"
				data-testid={"carousel-container"}
			>
				{videoData.map((video: IVideoData, index) => (
					<Wrapper
						className="carousel-wrapper"
						data-testid={"carousel-wrapper"}
						key={video._id}
						posterURL={video.videoPosterURL}
						isLandscape={video.videoDisplayMode === VideoDisplayModeEnum.LANDSCAPE}
						onClick={(): void => {
							if (currentPlayingVideo) {
								unloadVideoUrl(currentPlayingVideo);
								setCurrentPlayingVideo(null);
								setIsHovered(null);
							}
							handleCTAClick(video._id);
						}}
						onMouseEnter={(e): void => {
							const videoEl =
                                e.currentTarget.querySelector("video");
							if (videoEl) {
								handleMouseEnter(videoEl, video);
							}
						}}
						onMouseLeave={(): void => setIsHovered(null)}
					>
						<VideoTag
							ref={(el): HTMLVideoElement | null => (videoRefs.current[index] = el)}
							data-src={video.videoURL}
							muted
							loop
							playsInline
							title={video.title}
							aria-describedby={video.description ? `video-description-${video._id}` : undefined}
						>
							{video.description && (
								<p id={`video-description-${video._id}`} hidden>
									{video.description}
								</p>
							)}
						</VideoTag>

						<PosterContainer className="gp-poster-con">
							<VideoPlayContainer>
								{
									<IconButton svgIcon={ PlayIcon} isPortrait={false}
										style={{ width: "60px" }} backEnabled={false}/>
								}
							</VideoPlayContainer>

							<Flex justifyContent="start">
								{video.showShopIcon ? (
									<IconButton svgIcon={ ShopIcon} isPortrait={false}
										style={{ width: "36px" }}
										backEnabled={false}/>
								) : (
									<Flex>{"  "}</Flex>
								)}
							</Flex>
							<Flex m={"11px"}>
								<AnimatedCTA
									isHovered={video._id === isHovered}
									p={video.showTitle ? "8px 13px 8px 3px" : "8px"}
								>
									<BlurBox radius="10px" />
									<IconButton svgIcon={ PlayIcon} isPortrait={false}
										style={{ padding: "3px", width: "28px", minWidth: "28px" }}
										backEnabled={false}/>


									{video.showTitle && (
										<CTAText>
											{video.title}
										</CTAText>
									)}
								</AnimatedCTA>
							</Flex>
						</PosterContainer>
					</Wrapper>
				))}
			</CarouselContainer>
		</GPCarousel>
	);
};
