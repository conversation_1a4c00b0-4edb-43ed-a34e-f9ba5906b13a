import React, {
	useRef,
	useState,
	useEffect
} from "react";
import { IVideoData } from "@player/app";

interface Caption {
	timeStamp: number;
	videoData: IVideoData;
	show: boolean;
	isPlayerControlsVisible: boolean;
}

export const CaptionOverlay: React.FC<Caption> = ({ timeStamp, videoData, show = false, isPlayerControlsVisible }) => {

	const xPos = videoData.captionData?.xPos;
	const yPos = videoData.captionData?.yPos;
	const textColor = videoData.captionData?.textColor;
	const backgroundColor = videoData.captionData?.backgroundColor;
	const fontSize = videoData.captionData?.fontSizePx;
	const captionText = videoData.captionData?.captionText;

	const [position, setPosition] = useState({ x: xPos, y: yPos });

	const divRef = useRef<HTMLDivElement>(null);
	const [width, setWidth] = useState(0);
	const [height, setHeight] = useState(0);
	const [absWidth, setAbsWidth] = useState(0);

	const [textIndex, setTextIndex] = useState(0);
	const [prevTimeStamp, setPrevTimeStamp] = useState(0);
	const [text, setText] = useState("");

	useEffect(() => {
		let tempTextIndex = textIndex;

		if (prevTimeStamp > timeStamp) {
			setTextIndex(0);

			//Setter doesn't update variable immediately
			tempTextIndex = 0;
		}

		let found = false;
		if (captionText) {
			for (let i = tempTextIndex; i < captionText.length; i++) {
				if (timeStamp < captionText[i].startTime)
					break;

				if (timeStamp >= captionText[i].startTime && timeStamp < captionText[i].endTime) {
					found = true;
					setText(captionText[i].text);
					if (i > tempTextIndex)
						setTextIndex(i);
					break;
				}
			}
		}

		if (!found)
			setText("");

		setPrevTimeStamp(timeStamp);
	}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	, [timeStamp]);

	useEffect(() => {
		if (divRef.current) {
			//Ensures calculation remains consistent in the event of overflow line-breaks
			const oneLineDiv = divRef.current.cloneNode(true) as HTMLDivElement;
			oneLineDiv.style.whiteSpace = "pre";
			oneLineDiv.style.visibility = "hidden";
			(oneLineDiv.children[0] as HTMLParagraphElement).style.whiteSpace = "unset";
			document.body.appendChild(oneLineDiv);

			const absRect = oneLineDiv.getBoundingClientRect();

			setAbsWidth(absRect.width);

			document.body.removeChild(oneLineDiv);
		}
	}, [text, fontSize, show, videoData.captionData?.enabled]);

	useEffect(() => {
		setPosition({ x: (xPos ?? 0) - (absWidth / 2), y: (yPos ?? 0) - (height / 2) });
	}, [absWidth, height, xPos, yPos]);

	// eslint-disable-next-line react-hooks/exhaustive-deps
	useEffect(() => {
		if (divRef.current) {
			const rect = divRef.current.getBoundingClientRect();
			if (width !== rect.width)
				setWidth(rect.width);
			if (height !== rect.height)
				setHeight(rect.height);
		}
	});

	if (videoData.captionData == undefined || !videoData.captionData.enabled || !show) return null;

	return <div ref={divRef}
		style={
			{
				position: "absolute",
				left: `clamp(0%, calc(50% + ${position.x}px), calc(100% - ${absWidth}px))`,
				top: `clamp(0%, calc(50% + ${position.y}px), calc(100% - ${height}px
					- ${isPlayerControlsVisible ? "50" : "0"}px))`,
				width: "fit-content",
				height: "fit-content",
				textAlign: "center",
				display: (text != "") ? "block" : "none",
				padding: "10px"
			}
		}
	>
		<p style={{
			borderRadius: "10px",
			backgroundColor: backgroundColor,
			color: textColor,
			fontFamily: "\"Readex Pro\"",
			fontSize: `${fontSize}px`,
			padding: "0.5rem",
			width: "fit-content",
			margin: "0",
			marginLeft: "auto",
			marginRight: "auto",
			fontWeight: "bold",
			whiteSpace: "pre-line",
			overflowWrap: "anywhere",
			wordBreak: "normal"
		}}>
			{text}
		</p>
	</div >;
};

