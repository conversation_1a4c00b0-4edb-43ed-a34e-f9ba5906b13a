import React from "react";
import styled from "styled-components/macro";
import { Flex } from "../Flex";
import { BlurBox } from "../Box";

const Container = styled(Flex)`
    position: relative;
    cursor: pointer;
    border-radius: 20%;
    justify-content: center;
    align-items: center;
    width: 29px;
    min-width: 29px;
    padding: 5px;
`;

const Icon = styled.svg`
	z-index: 1;
    fill: ${(props): string => props.theme.textColor};
	width: -webkit-fit-content;
	height:auto;
`;

interface IProps {
	svgIcon: React.FC<React.SVGProps<SVGSVGElement>>;
	isPortrait: boolean | null;
	onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
	style?: React.CSSProperties;
	backEnabled?: boolean;
}

export const IconButton: React.FC<IProps> =
	({ svgIcon, isPortrait, onClick, style = {}, backEnabled = true }) => {

		const appStyle: React.CSSProperties = {};
		if (isPortrait) {
			appStyle.minWidth = "31px";
		}

		return (
			<Container style={{ ...appStyle, ...style }} onClick={onClick}>
				{backEnabled && <BlurBox radius="20%" />}
				<Icon as={svgIcon} />
			</Container>
		);
	};

