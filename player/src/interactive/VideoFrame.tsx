import React, {
	useState,
	useEffect,
	useRef,
	useCallback
} from "react";
import ObjectID from "bson-objectid";
import {
	IVideoData,
	SnippetTypeEnum,
	appSessionIdAtom,
	isAppInPreviewModeAtom,
	isPortraitAtom,
	isControlsVisibleAtom
} from "@player/app";
import {
	ISliderVideoProgress,
	LayoutPosEnum,
	SwipeDirectionEnum,
	sliderConfigAtom,
	sliderVideoPlayerAtom,
	ISliderVideoPlayer
} from "@player/slider";
import { notifyCoreToCloseApp } from "@player/app/app.message";
import {
	EventNameEnum,
	sendAppEvent
} from "@player/event";
import { Background } from "./Background";
import {
	VideoContainer,
	VideoPlayContainer
} from "./Components";
import {
	useRecoilState,
	useRecoilValue
} from "recoil";
import { IconButton } from "@player/components/button/IconButton";
import { default as PlayIcon } from "@player/assets/icon-play.svg";
import { useVideoFrameVisibility } from "@player/interactive/useVideoFrameVisibility";
import { PlayerControls } from "@player/player/PlayerControls";
import { InactiveVideoFrame } from "@player/interactive/InactiveVideoFrame";
import { CaptionOverlay } from "@player/components/captions/CaptionOverlay";

const playedSecondsInterval = 5;
interface Props {
	isViewable: boolean;
	videoData: IVideoData;
	playingAudio: boolean;
	setPlayingAudio: React.Dispatch<React.SetStateAction<boolean>>;
	position?: LayoutPosEnum;
	handleChevronClick?: (pos: LayoutPosEnum) => void;
	snippetType?: SnippetTypeEnum;
	setShowVideoOverlay: React.Dispatch<React.SetStateAction<boolean>>;
	showVideoOverlay: boolean;
	handleCTAChevronClick?: (dir: SwipeDirectionEnum) => void;
	setSwipeEnabled?: React.Dispatch<React.SetStateAction<boolean>>;
	playerContainerRef?: React.RefObject<HTMLDivElement>;
}

// eslint-disable-next-line max-lines-per-function
export const VideoFrame: React.FC<Props> = ({
	isViewable,
	videoData,
	playingAudio,
	setPlayingAudio,
	position,
	handleChevronClick,
	snippetType,
	setShowVideoOverlay,
	showVideoOverlay,
	handleCTAChevronClick,
	setSwipeEnabled,
	playerContainerRef
}) => {
	const appSessionId = useRecoilValue(appSessionIdAtom);
	const [sliderVideoPlayer, setSliderVideoPlayer] = useRecoilState(sliderVideoPlayerAtom);
	const sliderConfig = useRecoilValue(sliderConfigAtom);
	const [playingVideo, setPlayingVideo] = useState<boolean>(false);
	const [progressBarPercent, setProgressBarPercent] = useState<number>(0);
	const [playCount, setPlayCount] = useState<number>(0);
	const isAppInPreviewMode = useRecoilValue(isAppInPreviewModeAtom);
	const isPortrait = useRecoilValue(isPortraitAtom);
	const [isFullScreenIOS, setIsFullScreenIOS] = useState(false);
	const [videoDuration, setVideoDuration] = useState<number>(0);
	const seekVideoFunctionRef = useRef<(time: number) => void>(() => {
		/* no-op */
	});
	const isControlsVisible = useRecoilValue(isControlsVisibleAtom);
	const [showCaptions, setShowCaptions] = useState(true);
	const [playedMilliseconds, setPlayedMilliseconds] = useState(0);
	const playId = useRef<string>(ObjectID().toHexString());
	const lastReportedPlayedSeconds = useRef<number>(0);
	useVideoFrameVisibility(isViewable, playingVideo, lastReportedPlayedSeconds.current);

	if (videoData.showTitle === undefined) videoData.showTitle = true;

	useEffect(() => {
		if (isViewable) {
			//A new video is viewable now, so lets cleanup any state to initialize correctly.
			playId.current = ObjectID().toHexString();
			lastReportedPlayedSeconds.current = 0;
			const videoPlayer: ISliderVideoPlayer = {
				videoId: videoData._id,
				videoState: "unloaded",
				totalSeconds: 0,
				playedPercent: 0,
				playedSeconds: 0,
				audioState: playingAudio,
				playId: playId.current
			};
			setSliderVideoPlayer(videoPlayer);
			setPlayCount(0);
			!snippetType && setShowVideoOverlay(false);
			if (snippetType && showVideoOverlay) {
				setPlayingVideo(false);
			} else {
				!isAppInPreviewMode && seekVideo(0);
				setPlayingVideo(true);
			}

		} else {
			setPlayingVideo(false);
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [isViewable, videoData]);

	useEffect(() => {
		if (playCount === 10) {
			handlePauseVideo();
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [playCount]);

	const handlePauseVideo = (): void => {
		setPlayingVideo(false);
		setShowVideoOverlay(true);
		setSwipeEnabled && setSwipeEnabled(false);
	};

	useEffect(() => {
		if (sliderVideoPlayer &&
			sliderVideoPlayer.playedSeconds > 0 &&
			sliderVideoPlayer.playedSeconds >= lastReportedPlayedSeconds.current) {
			sendAppEvent({
				collectionId: sliderConfig.collectionId,
				accountId: sliderConfig.accountId,
				appSessionId: appSessionId,
				eventName: EventNameEnum.VIDEO_PLAY_POSITION,
				sliderVideoPlayer: sliderVideoPlayer,
				videoPlayStatus: playingVideo ? "playing" : "stopped",
				isShareMode: sliderConfig.isShareMode
			}, isAppInPreviewMode);
			lastReportedPlayedSeconds.current = sliderVideoPlayer?.playedSeconds;
		}
	}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	, [playingVideo]);

	const handleVideoEnded = (): void => {
		let videoPlayer: ISliderVideoPlayer | null = null;
		if (sliderVideoPlayer) {
			videoPlayer = {
				...sliderVideoPlayer,
				...{ videoState: "loaded", playedPercent: 100 }
			};
		}
		sendAppEvent({
			collectionId: sliderConfig.collectionId,
			accountId: sliderConfig.accountId,
			appSessionId: appSessionId,
			eventName: EventNameEnum.VIDEO_PLAY_POSITION,
			sliderVideoPlayer: videoPlayer,
			endedMethod: "loop",
			videoPlayStatus: "stopped",
			isShareMode: sliderConfig.isShareMode
		}, isAppInPreviewMode);
		setPlayCount((prev) => prev + 1);
		playId.current = ObjectID().toHexString();
		lastReportedPlayedSeconds.current = 0;
	};

	const handleScreenClick = (): void => {
		if (!playingVideo) {
			// video is paused and video overlay is ON due to loop max.
			setPlayCount(0);
			setPlayingVideo(true);
			setShowVideoOverlay(false);
			setSwipeEnabled && setSwipeEnabled(true);
		} else {
			// video is playing and video overlay is ON/OFF (depending on snippetType) => toggle audio
			if (showVideoOverlay) {
				setShowVideoOverlay(false);
			}
			setPlayingAudio((prev) => !prev);
		}
		if (snippetType && showVideoOverlay) {
			setPlayingAudio(true);
			sendAppEvent({
				collectionId: sliderConfig.collectionId,
				accountId: sliderConfig.accountId,
				appSessionId: appSessionId,
				eventName: EventNameEnum.THUMBNAIL_CTA_PRESS,
				videoId: videoData._id,
				snippet: { type: snippetType }
			}, isAppInPreviewMode);
			setSwipeEnabled && setSwipeEnabled(true);
		}

	};
	const handleCloseClick = (event: React.MouseEvent<HTMLDivElement>): void => {
		event.stopPropagation();
		notifyCoreToCloseApp({
			videoPlayer: sliderVideoPlayer,
			sliderConfig: sliderConfig,
			appSessionId: appSessionId
		});
	};

	const handleSetVideoSeekHandler = useCallback((seekFunction: (time: number) => void) => {
		seekVideoFunctionRef.current = seekFunction;
	}, []);

	const seekVideo = useCallback((time: number) => {
		if (seekVideoFunctionRef.current) {
			seekVideoFunctionRef.current(time);
		}
	}, []);

	const handleVideoTimeUpdate = (input: ISliderVideoProgress): void => {
		if (!isViewable || !sliderVideoPlayer?.playId) return;

		setPlayedMilliseconds(input.playedSeconds);
		const totalSecondsInt = Math.floor(input.totalSeconds);
		if (totalSecondsInt < 1) return;
		const playedSecondsInt = Math.floor(input.playedSeconds);
		const playedPercentInt = Math.floor((playedSecondsInt / totalSecondsInt) * 100);
		if (playedPercentInt < 0 || playedPercentInt > 100) {
			console.error("Invalid playedPercentInt: ", playedPercentInt);
			return;
		}
		const sliderVideoPlayerUpdated: ISliderVideoPlayer = {
			videoId: videoData._id,
			videoState: "loaded",
			totalSeconds: totalSecondsInt,
			playedPercent: playedPercentInt,
			playedSeconds: playedSecondsInt,
			audioState: playingAudio,
			playId: playId.current
		};
		setSliderVideoPlayer(sliderVideoPlayerUpdated);
		setProgressBarPercent(input.playedPercent);
		setVideoDuration(input.totalSeconds);
		if (playedSecondsInt >= lastReportedPlayedSeconds.current + playedSecondsInterval) {
			sendAppEvent({
				collectionId: sliderConfig.collectionId,
				accountId: sliderConfig.accountId,
				appSessionId: appSessionId,
				eventName: EventNameEnum.VIDEO_PLAY_POSITION,
				sliderVideoPlayer: sliderVideoPlayerUpdated,
				videoPlayStatus: "playing",
				isShareMode: sliderConfig.isShareMode
			}, isAppInPreviewMode);
			lastReportedPlayedSeconds.current = playedSecondsInt;
		}
	};

	const videoContainerStyle: React.CSSProperties = {
		borderRadius: position ? "10px" : "0px",
		cursor: isControlsVisible ? "pointer" : "none"
	};

	return (
		<VideoContainer
			data-testid="shoppable-video-con"
			style={{ ...videoContainerStyle }}
			onClick={(e: React.MouseEvent<HTMLDivElement>): void => {
				e.stopPropagation();
				if (isViewable) {
					handleScreenClick();
				} else if (position && handleChevronClick) {
					handleChevronClick(position);
				} else {
					return undefined;
				}
			}}
		>
			<Background
				videoData={videoData}
				playingVideo={playingVideo}
				playingAudio={playingAudio}
				handleVideoTimeUpdate={handleVideoTimeUpdate}
				handleVideoEnded={handleVideoEnded}
				handleSetVideoSeekHandler={handleSetVideoSeekHandler}
				isFullScreenIOS={isFullScreenIOS}
				setIsFullScreenIOS={setIsFullScreenIOS}
			/>

			<CaptionOverlay videoData={videoData}
				timeStamp={playedMilliseconds}
				show={isViewable && showCaptions} />

			{isViewable && showVideoOverlay && (
				<VideoPlayContainer>
					<IconButton svgIcon={PlayIcon}
						isPortrait={isPortrait}
						style={{ width: "60px", backdropFilter: "none" }} backEnabled={false} />
				</VideoPlayContainer>
			)}

			{!isViewable && position && <InactiveVideoFrame videoData={videoData} position={position} />}

			{isViewable && <PlayerControls
				videoData={videoData}
				progressBarPercent={progressBarPercent}
				playingAudio={playingAudio}
				playingVideo={playingVideo}
				setPlayingAudio={setPlayingAudio}
				setPlayingVideo={setPlayingVideo}
				setShowVideoOverlay={setShowVideoOverlay}
				playerContainerRef={playerContainerRef}
				videoDuration={videoDuration}
				seekVideo={seekVideo}
				setIsFullScreenIOS={setIsFullScreenIOS}
				handleCloseClick={handleCloseClick}
				snippetType={snippetType}
				handleCTAChevronClick={handleCTAChevronClick}
				handleCCClick={():void => { setShowCaptions(!showCaptions); }}
				isCaptionsEnabled={videoData.captionData?.enabled ?? false} />
			}

		</VideoContainer>
	);
};
