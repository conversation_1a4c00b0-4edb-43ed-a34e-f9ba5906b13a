import {
	IProductData,
	IVideoData,
	AccountManifest,
	SubscriptionTypeEnum,
	VideoDisplayModeEnum,
	CollectionDataCache,
	VideoDataCache
} from "../../app/app.interface";

const products: IProductData[] = [
	{
		title: "hoodie",
		url: "http://example/hoodie",
		productThumbnail: "http://example/image.png",
		subTitle: "$20",
		_id: "6426ead942a60c88eeb200f8"
	},
	{
		title: "hat",
		url: "http://example/hat",
		productThumbnail: "http://example/image.png",
		subTitle: "$30",
		_id: "6426ead942a60c88eeb200f9"
	}
];

export const videoDataMock: { interactiveVideos: IVideoData[] } = {
	interactiveVideos: [
		{
			_id: "v1",
			accountId: "123",
			collectionId: "c1",
			title: "tree story",
			description: "This is a tree story.",
			videoURL: "http://example/video.mp4",
			videoPosterURL: "http://example/image.png",
			products: products,
			ctaText: "Shop Now",
			showShopIcon: true,
			showTitle: true,
			createdAt: *************,
			updatedAt: *************,
			videoDisplayMode: VideoDisplayModeEnum.PORTRAIT,
			phone: "**********",
			email: "<EMAIL>"
		},
		{
			_id: "v11",
			accountId: "123",
			collectionId: "c1",
			title: "tree story",
			description: "This is a tree story.",
			videoURL: "http://example/video.mp4",
			videoPosterURL: "http://example/image.png",
			products: products,
			ctaText: "Shop Now",
			showShopIcon: false,
			showTitle: false,
			createdAt: *************,
			updatedAt: *************,
			videoDisplayMode: VideoDisplayModeEnum.PORTRAIT
		}
	]
};

export const gpSessionIdMock = "88aa1b44d5f4aabf6b3d5d02";
export const apiUrlMock = "https://api.domain.tld/";
export const playerUrlMock = "https://app.domain.tld/";
export const cdnUrlMock = "https://cdn.domain.tld/";

export const appSessionIdMock = "65cf81dd4a35151c0001a822";

export const accountManifestMock: AccountManifest = {
	allowLandscape: false,
	allowCTALead: true,
	allowThemes: false,
	allowSharing: true,
	hideVanityBranding: false,
	subscriptionType: SubscriptionTypeEnum.BASIC
};

export const sliderConfigMock = {
	accountId: videoDataMock.interactiveVideos[0].accountId,
	collectionId: videoDataMock.interactiveVideos[0].collectionId,
	hideVanityBranding: accountManifestMock.hideVanityBranding,
	isShareMode: false
};

export const collectionDataCacheMock: CollectionDataCache = {
	accountId: "123",
	interactiveVideos: videoDataMock.interactiveVideos,
	buttonBackgroundColor: "#000000",
	buttonBackgroundBlur: true,
	iconTextColor: "#ffffff",
	displayFont: "Arial",
	carouselBorderRadius: 10,
	carouselIsCentered: false,
	carouselMargin: 0,
	carouselGap: 24,
	widgetBorderRadius: 10,
	widgetPosition: "right",
	inlineBorderRadius: 10
};

export const videoDataCacheMock: VideoDataCache = {
	accountId: "123",
	interactiveVideo: videoDataMock.interactiveVideos[0]
};

