export enum SnippetTypeEnum {
	CAROUSEL = "carousel",
	WIDGET = "widget",
	ONSITE = "onsite",
	PLAYER = "player"
}

export enum VideoDisplayModeEnum {
	PORTRAIT = "portrait",
	LANDSCAPE = "landscape"
}
export interface IProductData {
	_id: string;
	title: string;
	url: string;
	productThumbnail?: string;
	subTitle?: string;
}
export interface IVideoData {
	_id: string;
	accountId: string;
	collectionId: string;
	title: string;
	description?: string;
	videoURL: string;
	videoPosterURL: string;
	products: IProductData[];
	createdAt: number;
	updatedAt: number;
	ctaText?: string;
	showShopIcon?: boolean;
	showTitle?: boolean;
	videoDisplayMode: VideoDisplayModeEnum;
	videoWidthPx?: number;
	videoHeightPx?: number;
	phone?: string;
	email?: string;
	captionData?: CaptionData;
}

export interface CaptionData {
	enabled: boolean;
	captionText: CaptionText[];
	textColor: string;
	backgroundColor: string;
	fontSizePx: number;
	xPos: number;
	yPos: number;
}

export interface CaptionText {
	startTime: number;
	endTime: number;
	text: string;
}

export const defaultVideo: IVideoData = {
	_id: "",
	accountId: "",
	collectionId: "",
	title: "",
	description: "",
	videoURL: "",
	videoPosterURL: "",
	products: [],
	createdAt: 0,
	updatedAt: 0,
	ctaText: "Shop Now",
	showShopIcon: true,
	showTitle: true,
	videoDisplayMode: VideoDisplayModeEnum.PORTRAIT
};
export interface IAppSessionStorage {
	likedVideos: string[];
}

export enum SubscriptionTypeEnum {
	BASIC = "basic",
	PRO = "pro"
}

export interface AccountManifest {
	allowLandscape: boolean;
	allowCTALead: boolean;
	allowThemes: boolean;
	allowSharing: boolean;
	hideVanityBranding: boolean;
	subscriptionType: SubscriptionTypeEnum;
}

export interface IAppPreviewData {
	video: IVideoData;
	account: {
		allowLandscape: boolean;
		allowCTALead: boolean;
		allowThemes: boolean;
		allowSharing: boolean;
		hideVanityBranding: boolean;
		subscriptionType: SubscriptionTypeEnum;
	};
}

export interface CollectionDataCache {
	accountId: string,
	interactiveVideos: Partial<IVideoData>[];
	buttonBackgroundColor: string;
	buttonBackgroundBlur: boolean;
	iconTextColor: string;
	displayFont: string;
	carouselBorderRadius: number;
	carouselIsCentered: boolean;
	carouselMargin: number;
	carouselGap: number;
	widgetBorderRadius: number;
	widgetPosition: "left" | "right";
	inlineBorderRadius: number;
}

export interface VideoDataCache {
	accountId: string,
	interactiveVideo: IVideoData;
}




