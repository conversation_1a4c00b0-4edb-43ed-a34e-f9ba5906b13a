import { atom } from "recoil";
import ObjectID from "bson-objectid";
import {
	IAppSessionStorage,
	IAppPreviewData
} from "./app.interface";


export const isPortraitAtom = atom<boolean | null>({
	key: "isPortrait",
	default: null
});

export const isControlsVisibleAtom = atom<boolean>({
	key: "isControlsVisible",
	default: true
});

export const isPlayerControlsVisibleAtom = atom<boolean>({
	key: "isPlayerControlsVisible",
	default: true
});

export const isUserInteractingAtom = atom<boolean>({
	key: "isUserInteracting",
	default: false
});

export const appSessionIdAtom = atom<string>({
	key: "appSessionId",
	default: ObjectID().toHexString()
});

export const appSessionStorageAtom = atom<IAppSessionStorage | null>({
	key: "appSessionStorage",
	default: null
});

export const isAppInPreviewModeAtom = atom<boolean>({
	key: "isAppInPreviewMode",
	default: false
});


export const appPreviewDataAtom = atom<IAppPreviewData | null>({
	key: "appPreviewData",
	default: null
});
