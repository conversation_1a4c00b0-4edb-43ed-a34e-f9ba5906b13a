import { SpeechToTextData } from "../speechToText/speechToText.interfaces";
import { CaptionText } from "../caption/caption.interface";

export class SRTModel {
	public convertFromSpeechToText(sttData: SpeechToTextData): string {
		if (!sttData.words.length) {
			return "";
		}

		let srtContent = "";
		let index = 1;
		let currentLine = "";
		let currentStartTime: { seconds: number; nanos: number; } | null = null;
		let currentEndTime: { seconds: number; nanos: number; } | null = null;

		for (const word of sttData.words) {
			if (!currentStartTime) {
				currentStartTime = word.startTime;
			}
			currentLine += (currentLine ? " " : "") + word.word;

			// Create a new subtitle every ~40 characters or at punctuation
			if (currentLine.length >= 40 || /[.!?]$/.test(word.word)) {
				currentEndTime = word.endTime;

				const startTimeSeconds = currentStartTime.seconds + currentStartTime.nanos / 1e9;
				const endTimeSeconds = currentEndTime.seconds + currentEndTime.nanos / 1e9;

				srtContent += this.createSRTContent(
					index,
					currentLine,
					startTimeSeconds,
					endTimeSeconds
				);

				index++;
				currentLine = "";
				currentStartTime = null;
				currentEndTime = null;
			}
		}

		if (currentLine && currentStartTime && sttData.words.length > 0) {
			const lastWord = sttData.words[sttData.words.length - 1];
			const startTimeSeconds = currentStartTime.seconds + currentStartTime.nanos / 1e9;
			const endTimeSeconds = lastWord.endTime.seconds + lastWord.endTime.nanos / 1e9;

			srtContent += this.createSRTContent(
				index,
				currentLine,
				startTimeSeconds,
				endTimeSeconds
			);
		}

		if (srtContent === "") {
			return "";
		}

		return srtContent.trim();
	}

	private formatTimeToSRT(seconds: number): string {
		const hours = Math.floor(seconds / 3600);
		seconds %= 3600;
		const minutes = Math.floor(seconds / 60);
		const remainingSeconds = seconds % 60;
		const wholeSeconds = Math.floor(remainingSeconds);
		const milliseconds = Math.floor((remainingSeconds - wholeSeconds) * 1000);

		return (
			`${hours.toString().padStart(2, "0")}:` +
			`${minutes.toString().padStart(2, "0")}:` +
			`${wholeSeconds.toString().padStart(2, "0")},` +
			`${milliseconds.toString().padStart(3, "0")}`
		);
	}

	private createSRTContent(
		index: number,
		currentLine: string,
		startTimeSeconds: number,
		endTimeSeconds: number
	): string {
		let srtContent = "";
		srtContent += `${index}\n`;
		srtContent += `${this.formatTimeToSRT(startTimeSeconds)} --> ${this.formatTimeToSRT(endTimeSeconds)}\n`;
		srtContent += `${currentLine.trim()}\n\n`;
		return srtContent;
	}

	public async parseToCaptionText(srtString: string): Promise<CaptionText[]> {
		function isNumber(str: string): boolean {
			return !isNaN(Number(str.trim()));
		}

		let captionIndex = 0;
		let captionTextIsNext = false;
		const captionText: CaptionText[] = [];

		const rawSrtSplit = srtString.split("\n");
		rawSrtSplit.forEach(text => {
			if (captionTextIsNext && text !== "" && captionText.length === captionIndex + 1) {
				captionText[captionIndex].text += (captionText[captionIndex].text === "" ? "" : "\n")
					+ text.replace(/\t/g, "");
			} else if (text.includes("-->")) {
				captionTextIsNext = true;
				const times = text.replace(/ /g, "").replace(/\t/g, "").split("-->");

				const startTimeParts = times[0].split(":");
				const startTime = {
					hours: Number(startTimeParts[0]),
					minutes: Number(startTimeParts[1]),
					seconds: Number(startTimeParts[2].split(",")[0]),
					milliseconds: Number(startTimeParts[2].split(",")[1])
				};

				startTime.seconds += startTime.hours * 3600 + startTime.minutes * 60 + (startTime.milliseconds / 1000);

				const endTimeParts = times[1].split(":");
				const endTime = {
					hours: Number(endTimeParts[0]),
					minutes: Number(endTimeParts[1]),
					seconds: Number(endTimeParts[2].split(",")[0]),
					milliseconds: Number(endTimeParts[2].split(",")[1])
				};

				endTime.seconds += endTime.hours * 3600 + endTime.minutes * 60 + (endTime.milliseconds / 1000);

				captionText[captionIndex] = { startTime: startTime.seconds, endTime: endTime.seconds, text: "" };
			} else {
				captionTextIsNext = false;
				if (isNumber(text)) captionIndex = Number(text) - 1;
			}
		});

		return captionText;
	}
}
