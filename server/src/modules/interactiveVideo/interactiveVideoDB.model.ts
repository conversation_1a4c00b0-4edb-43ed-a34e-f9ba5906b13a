import mongoose, { Schema } from "mongoose";
import {
	VideoDisplayModeEnum,
	IShoppableVideo
} from "./interactiveVideo.interface";
import { CaptionSchema } from "../caption/captionDB.model";

const ProductSchema: Schema = new Schema({
	title: { type: Schema.Types.String, required: true },
	url: { type: Schema.Types.String, required: true },
	productThumbnail: { type: Schema.Types.String, required: false },
	productDescription: { type: Schema.Types.String, required: false },
	subTitle: { type: Schema.Types.String, required: false }
});


const LinkClicksSchema: Schema = new Schema(
	{
		productId: Schema.Types.ObjectId,
		productTitle: { type: Schema.Types.String },
		productURL: { type: Schema.Types.String },
		productImageURL: { type: Schema.Types.String },
		clickCount: { type: Schema.Types.Number }
	},
	{ _id: false }
);

const ShoppableVideoSchema: Schema = new Schema(
	{
		accountId: { type: Schema.Types.ObjectId, required: true },
		title: { type: Schema.Types.String, required: true },
		description: { type: Schema.Types.String, required: false, default: "" },
		videoURL: { type: Schema.Types.String, required: true },
		videoPosterURL: { type: Schema.Types.String, required: true },
		videoPosterPlayEmbedURL: { type: Schema.Types.String, required: true },
		gifURL: { type: Schema.Types.String, required: true },
		ctaText: { type: Schema.Types.String, required: true },
		showShopIcon: { type: Schema.Types.Boolean, required: true },
		showTitle: { type: Schema.Types.Boolean, required: true },
		products: [ProductSchema],
		createdAt: {
			type: Number,
			default: () => Date.now()
		},
		updatedAt: {
			type: Number,
			default: () => Date.now()
		},
		videoTotalSeconds: { type: Schema.Types.Number, default: 0 },
		videoPlayCount: { type: Schema.Types.Number, default: 0 },
		videoUniquePlayCount: { type: Schema.Types.Number, default: 0 },
		videoPlayDurationSeconds: { type: Schema.Types.Number, default: 0 },
		videoUniquePlayDurationSeconds: { type: Schema.Types.Number, default: 0 },
		videoScore: { type: Schema.Types.Number, default: 0 },
		linkClicks: { type: [LinkClicksSchema], default: [] },
		playPercentCount20: { type: Schema.Types.Number, default: 0 },
		playPercentCount40: { type: Schema.Types.Number, default: 0 },
		playPercentCount60: { type: Schema.Types.Number, default: 0 },
		playPercentCount80: { type: Schema.Types.Number, default: 0 },
		playPercentCount100: { type: Schema.Types.Number, default: 0 },
		likes: { type: Schema.Types.Number, default: 0 },
		videoDisplayMode: { type: Schema.Types.String, default: VideoDisplayModeEnum.PORTRAIT },
		videoId: { type: Schema.Types.ObjectId, required: false },
		videoWidthPx: { type: Schema.Types.Number, required: false },
		videoHeightPx: { type: Schema.Types.Number, required: false },
		phone: { type: Schema.Types.String, required: false, default: null },
		email: { type: Schema.Types.String, required: false, default: null },
		phonePressCount: { type: Schema.Types.Number, default: 0 },
		emailSubmitCount: { type: Schema.Types.Number, default: 0 },
		captionData: { type: CaptionSchema, required: false, default: null }
	},
	{ timestamps: true }
);

export const InteractiveVideoDBModel = mongoose.model<IShoppableVideo>(
	"ShoppableVideos",
	ShoppableVideoSchema
);
