import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	IPutShoppableVideoPayload,
	IShoppableVideoProduct,
	InteractiveVideoUpdateOne
} from "./interactiveVideo.interface";
import {
	startDBTransaction,
	completeDBTransaction,
	cancelDBTransaction
} from "../../services/mongodb/transaction.service";
import { InteractiveVideoModel } from "./interactiveVideo.model";

export const putShoppableVideoController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.accountToken?.account?._id) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing accountId in account token");
		}

		await startDBTransaction(res.locals.session);

		const interactiveVideoModel = new InteractiveVideoModel(res.locals.session);
		const videoDocument = await interactiveVideoModel.readOneById(req.params.videoId);

		if (videoDocument.accountId.toString() !== req.accountToken.account._id) {
			throw new APIError(APIErrorName.E_ACCESS_FORBIDDEN, "Mixed accountId in interactiveVideo and token");
		}

		const requestBody = req.body as IPutShoppableVideoPayload;

		const productTitles: string[] = [
			requestBody.productTitle0,
			requestBody.productTitle1,
			requestBody.productTitle2,
			requestBody.productTitle3,
			requestBody.productTitle4,
			requestBody.productTitle5,
			requestBody.productTitle6,
			requestBody.productTitle7,
			requestBody.productTitle8,
			requestBody.productTitle9
		];

		const productURLs: string[] = [
			requestBody.productURL0,
			requestBody.productURL1,
			requestBody.productURL2,
			requestBody.productURL3,
			requestBody.productURL4,
			requestBody.productURL5,
			requestBody.productURL6,
			requestBody.productURL7,
			requestBody.productURL8,
			requestBody.productURL9
		];

		const productImageURLs: string[] = [
			requestBody.productImageURL0,
			requestBody.productImageURL1,
			requestBody.productImageURL2,
			requestBody.productImageURL3,
			requestBody.productImageURL4,
			requestBody.productImageURL5,
			requestBody.productImageURL6,
			requestBody.productImageURL7,
			requestBody.productImageURL8,
			requestBody.productImageURL9
		];

		const productDescriptions: string[] = [
			requestBody.productDescription0,
			requestBody.productDescription1,
			requestBody.productDescription2,
			requestBody.productDescription3,
			requestBody.productDescription4,
			requestBody.productDescription5,
			requestBody.productDescription6,
			requestBody.productDescription7,
			requestBody.productDescription8,
			requestBody.productDescription9
		];

		const productSubTitle: string[] = [
			requestBody.subTitle0,
			requestBody.subTitle1,
			requestBody.subTitle2,
			requestBody.subTitle3,
			requestBody.subTitle4,
			requestBody.subTitle5,
			requestBody.subTitle6,
			requestBody.subTitle7,
			requestBody.subTitle8,
			requestBody.subTitle9
		];

		const videoProducts: IShoppableVideoProduct[] = [];

		for (let i = 0; i < 10; i++) {
			if (productTitles[i] && productURLs[i]) {
				// eslint-disable-next-line no-await-in-loop
				const videoProduct = <IShoppableVideoProduct> {
					title: productTitles[i],
					url: productURLs[i],
					productThumbnail: productImageURLs[i] ? productImageURLs[i] : ""
				};
				if (productDescriptions[i]){
					videoProduct.productDescription = productDescriptions[i];
				}
				if (productSubTitle[i]){
					videoProduct.subTitle = productSubTitle[i];
				}
				videoProducts.push(videoProduct);
			}
		}

		// eslint-disable-next-line no-console
		console.log(
			`debug trace updateOneById from putShoppableVideoController. videoId: ${videoDocument._id.toString()}`
		);

		const updateVideoData: InteractiveVideoUpdateOne = {
			title: requestBody.title,
			description: requestBody.description,
			videoURL: requestBody.videoURL,
			products: videoProducts,
			showTitle: requestBody.showTitle === "true",
			ctaText: requestBody.ctaText,
			videoPosterURL: requestBody.videoPosterImageURL,
			videoPosterPlayEmbedURL: requestBody.videoPosterPlayEmbedURL,
			videoDisplayMode: requestBody.videoDisplayMode,
			gifURL: requestBody.gifURL,
			phone: requestBody.phone === "" ? null : requestBody.phone,
			email: requestBody.email === "" ? null : requestBody.email,
			captionData: (requestBody.captionData == undefined)
				? undefined : JSON.parse(requestBody.captionData)
		};

		const updatedVideo = await interactiveVideoModel.updateOneById(videoDocument._id.toString(), updateVideoData);

		const result = {
			shoppableVideo: (await interactiveVideoModel.redactData([updatedVideo]))[0]
		};

		await completeDBTransaction(res.locals.session);

		return res.status(200).json(result);
	} catch (error: unknown) {
		await cancelDBTransaction(res.locals.session);
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};
