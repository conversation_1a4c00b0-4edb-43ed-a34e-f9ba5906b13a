import mongoose, {
	Document,
	ObjectId
} from "mongoose";
import { CaptionData } from "../caption/caption.interface";

export interface IShoppableVideoProduct {
	_id: mongoose.Types.ObjectId;
	title: string;
	url: string;
	productThumbnail?: string;
	productDescription?: string;
	subTitle?: string;
}

export enum VideoDisplayModeEnum {
	PORTRAIT = "portrait",
	LANDSCAPE = "landscape"
}

export interface IShoppableVideo extends Document {
	_id: ObjectId;
	accountId: ObjectId;
	createdAt: Date;
	title: string;
	description: string;
	videoURL: string;
	videoPosterURL: string;
	videoPosterPlayEmbedURL: string;
	gifURL: string;
	products: IShoppableVideoProduct[];
	ctaText: string;
	showTitle: boolean;
	videoTotalSeconds: number;
	videoPlayCount: number;
	videoUniquePlayCount: number;
	videoPlayDurationSeconds: number;
	videoUniquePlayDurationSeconds: number;
	videoScore: number;
	linkClicks: {
		productId: ObjectId,
		productTitle: string,
		productURL: string,
		productImageURL: string,
		clickCount: number,
	}[];
	playPercentCount20: number;
	playPercentCount40: number;
	playPercentCount60: number;
	playPercentCount80: number;
	playPercentCount100: number;
	likes: number;
	videoDisplayMode: VideoDisplayModeEnum;
	videoId: ObjectId;
	videoWidthPx: number;
	videoHeightPx: number;
	phonePressCount: number;
	emailSubmitCount: number;
	phone?: string | null;
	email?: string | null;
	updatedAt: Date;
	captionData?: CaptionData | null;
}

export interface IGetShoppableVideoPayload {
	limit: string;
	sortKey: string;
	SortBy: "asc" | "dsc";
}

export interface IPostVideoPayload {
	title: string;
	description: string;
	videoURL: string;
	productTitle0: string;
	productTitle1: string;
	productTitle2: string;
	productTitle3: string;
	productTitle4: string;
	productTitle5: string;
	productTitle6: string;
	productTitle7: string;
	productTitle8: string;
	productTitle9: string;
	productURL0: string;
	productURL1: string;
	productURL2: string;
	productURL3: string;
	productURL4: string;
	productURL5: string;
	productURL6: string;
	productURL7: string;
	productURL8: string;
	productURL9: string;
	productImageURL0: string;
	productImageURL1: string;
	productImageURL2: string;
	productImageURL3: string;
	productImageURL4: string;
	productImageURL5: string;
	productImageURL6: string;
	productImageURL7: string;
	productImageURL8: string;
	productImageURL9: string;
	productDescription0: string;
	productDescription1: string;
	productDescription2: string;
	productDescription3: string;
	productDescription4: string;
	productDescription5: string;
	productDescription6: string;
	productDescription7: string;
	productDescription8: string;
	productDescription9: string;
	subTitle0: string;
	subTitle1: string;
	subTitle2: string;
	subTitle3: string;
	subTitle4: string;
	subTitle5: string;
	subTitle6: string;
	subTitle7: string;
	subTitle8: string;
	subTitle9: string;
	ctaText: string;
	showTitle: string;
	videoPosterImageURL: string;
	videoPosterPlayEmbedURL: string;
	gifURL: string;
	captionData?: string;
	videoDisplayMode: VideoDisplayModeEnum;
	videoId: string;
	phone: string;
	email: string;
}

export interface IPutShoppableVideoPayload {
	title: string;
	description: string;
	videoURL: string;
	productTitle0: string;
	productTitle1: string;
	productTitle2: string;
	productTitle3: string;
	productTitle4: string;
	productTitle5: string;
	productTitle6: string;
	productTitle7: string;
	productTitle8: string;
	productTitle9: string;
	productURL0: string;
	productURL1: string;
	productURL2: string;
	productURL3: string;
	productURL4: string;
	productURL5: string;
	productURL6: string;
	productURL7: string;
	productURL8: string;
	productURL9: string;
	productImageURL0: string;
	productImageURL1: string;
	productImageURL2: string;
	productImageURL3: string;
	productImageURL4: string;
	productImageURL5: string;
	productImageURL6: string;
	productImageURL7: string;
	productImageURL8: string;
	productImageURL9: string;
	productDescription0: string;
	productDescription1: string;
	productDescription2: string;
	productDescription3: string;
	productDescription4: string;
	productDescription5: string;
	productDescription6: string;
	productDescription7: string;
	productDescription8: string;
	productDescription9: string;
	subTitle0: string;
	subTitle1: string;
	subTitle2: string;
	subTitle3: string;
	subTitle4: string;
	subTitle5: string;
	subTitle6: string;
	subTitle7: string;
	subTitle8: string;
	subTitle9: string;
	ctaText: string;
	showTitle: string;
	videoPosterImageURL: string;
	videoPosterPlayEmbedURL: string;
	gifURL: string;
	captionData?: string;
	videoDisplayMode: VideoDisplayModeEnum;
	phone: string;
	email: string;
}

export interface InteractiveVideoUpdateOne {
	title: string;
	description: string;
	videoURL: string;
	videoPosterURL: string;
	videoPosterPlayEmbedURL: string;
	gifURL: string;
	products: IShoppableVideoProduct[];
	ctaText: string;
	showTitle: boolean;
	videoDisplayMode: VideoDisplayModeEnum;
	phone: string | null;
	email: string | null;
	captionData?: string | null;
}

export interface InteractiveVideoCreateOne {
	title: string;
	description: string;
	videoURL: string;
	videoPosterURL: string;
	videoPosterPlayEmbedURL: string;
	gifURL: string;
	products: IShoppableVideoProduct[];
	ctaText: string;
	showTitle: boolean;
	videoDisplayMode: VideoDisplayModeEnum;
	videoWidthPx?: number;
	videoHeightPx?: number;
	videoId?: ObjectId;
	phone?: string | null;
	email?: string | null;
	captionData?: CaptionData | null;
}

export interface InteractiveVideoReadManyByAccountId {
	accountId: string,
	sortKey?: string,
	sortBy?: "asc" | "dsc",
	limit?: number
}
