import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { LocaleAPI } from "../../interfaces/apiTypes";
import TestHelper from "../../__tests__/mocks/testHelper";
import { ISignupEmailPayload } from "../signup/signup.interfaces";
import { IVideo } from "../video/video.interfaces";
import { InteractiveVideoModel } from "../interactiveVideo/interactiveVideo.model";

describe("White Box Testing | interactiveCollection Model", () => {
	let expressApp: express.Express;
	let testHelper: TestHelper;
	let accountId: string;
	let interactiveVideoId: string;
	const signupEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld"
	};

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
		testHelper = new TestHelper(expressApp);
		const { accessToken } = await testHelper.signupEmail(signupEmailPayload);
		const account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();
		const accountToken = await testHelper.getAccountToken(accountId, accessToken);
		const video: IVideo = await testHelper.createVideo(account._id.toString());
		const videoId = video._id.toString();
		const shoppableVideo = await testHelper.createShoppableVideo(videoId, accountToken, accessToken);
		interactiveVideoId = shoppableVideo._id.toString();
	});

	it("should register email submit metric successfully.", async () =>{
		const interactiveVideoModel = new InteractiveVideoModel(null);
		const interactiveVideo = await interactiveVideoModel.readOneById(interactiveVideoId);
		const redactedSortedVideos = await interactiveVideoModel.constructPublicData([interactiveVideo]);

		expect(redactedSortedVideos[0]).not.toEqual(interactiveVideo);
		expect(redactedSortedVideos).toEqual([
			{
				_id: interactiveVideo._id,
				title: interactiveVideo.title,
				description: interactiveVideo.description,
				videoURL: interactiveVideo.videoURL,
				videoPosterURL: interactiveVideo.videoPosterURL,
				products: interactiveVideo.products,
				ctaText: interactiveVideo.ctaText,
				showTitle: interactiveVideo.showTitle,
				videoDisplayMode: interactiveVideo.videoDisplayMode,
				videoWidthPx: interactiveVideo.videoWidthPx,
				videoHeightPx: interactiveVideo.videoHeightPx,
				videoPosterPlayEmbedURL: interactiveVideo.videoPosterPlayEmbedURL,
				phone: interactiveVideo.phone,
				email: interactiveVideo.email
			}
		]);
		expect(Object.keys(redactedSortedVideos[0])).toEqual([
			"_id",
			"title",
			"description",
			"videoURL",
			"videoPosterURL",
			"products",
			"ctaText",
			"showTitle",
			"videoDisplayMode",
			"videoWidthPx",
			"videoHeightPx",
			"videoPosterPlayEmbedURL",
			"phone",
			"email"
		]);
	});
});
