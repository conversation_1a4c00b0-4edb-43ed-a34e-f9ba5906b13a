import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";
import { BucketModel } from "../bucket/bucket.model";
import {
	CDN_DIR,
	validateBackslash
} from "../../utils/helpers/gp.helper";
import { getSecrets } from "../secrets/secrets.model";
import sharp, {
	FormatEnum,
	ResizeOptions
} from "sharp";
import { IJob } from "../job/job.interfaces";
import mongoose, { SaveOptions } from "mongoose";
import {
	JobsType,
	JobsStatus
} from "../job/jobs.enums";
import { JobDBModel } from "../job/jobDB.model";
import os from "os";
import { promises as fsPromises } from "fs";

export interface ImageOptimize {
	sourceURL: string;
	imageWidthPx?: number;
	imageHeightPx?: number;
}

export class ImageModel {
	private session: mongoose.ClientSession | null;

	constructor (session: mongoose.ClientSession | null) {
		this.session = session;
	}

	async createImageOptimizeJob(accountId: string, userId: string, createData: ImageOptimize):
	Promise<IJob> {
		try {
			const imageResult = await this.optimizeImage(accountId, userId, createData);

			const options: SaveOptions = { session: this.session };
			const data = {
				tempFilename: imageResult.filename,
				accountId: new mongoose.Types.ObjectId(accountId),
				userId: new mongoose.Types.ObjectId(userId),
				type: JobsType.OPTIMIZE_IMAGE,
				status: JobsStatus.COMPLETE,
				statusMessage: "image optimization job completed successfully.",
				sourceURL: createData.sourceURL,
				imageWidthPx: createData.imageWidthPx,
				imageHeightPx: createData.imageHeightPx,
				imageURL: imageResult.imageURL
			};

			const job = await new JobDBModel(data).save(options);
			return job;
		} catch (error: unknown) {
			if (error instanceof Error && error.name === "MongoServerError" && (error as any).code === 11000) {
				throw new APIError(APIErrorName.E_FILE_UPLOAD_EXISTING_FILENAME, "FILENAME already exists");
			}
			throw error;
		}
	}

	private async optimizeImage(
		accountId: string,
		userId: string,
		imageData: ImageOptimize
	): Promise<{filename: string, imageURL: string}> {
		let filePath: string | null = null;

		try {
			const secrets = await getSecrets();

			const imageResponse = await fetch(imageData.sourceURL);

			if (!imageResponse.ok) {
				throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid image URL");
			}

			const imageBuffer = await imageResponse.arrayBuffer();
			const srcFilename = imageData.sourceURL.split("/").pop() ?? "";

			if (!srcFilename) {
				throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid image URL");
			}

			const sizeOptions = this.readSizeOptions(imageData.imageWidthPx, imageData.imageHeightPx);

			const fileType = "jpg";
			const resizedImageBuffer = await sharp(imageBuffer)
				.resize(sizeOptions)
				.toFormat(fileType as keyof FormatEnum)
				.toBuffer();

			const resizedImageMetadata = await sharp(resizedImageBuffer).metadata();

			const imageWidth: number = resizedImageMetadata.width ?? 0;
			const imageHeight: number = resizedImageMetadata.height ?? 0;

			const objectId = new mongoose.Types.ObjectId();
			const imageSuffix = `-${objectId}_${imageWidth}x${imageHeight}`;
			const finalFilename = BucketModel.sanitizeFileName(srcFilename, imageSuffix);

			filePath = `${os.tmpdir()}/${finalFilename}`;

			await sharp(resizedImageBuffer).toFile(filePath);

			const bucketModel = new BucketModel(secrets.storage.bucketName);
			const fileLocation = `${CDN_DIR.MEDIA}${accountId}/${finalFilename}`;
			const oneYear = ********;
			const cacheControl = `public, max-age=${oneYear}`;

			await bucketModel.uploadFileToBucketFromStream(
				filePath,
				`image/${fileType}`,
				fileLocation,
				cacheControl
			);

			return {
				filename: finalFilename,
				imageURL: `${validateBackslash(secrets.cdn.host)}/${fileLocation}`
			};
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error).setDetail({
				info: "Failed to optimize image"
			});
		} finally {
			if (filePath) {
				try {
					await fsPromises.unlink(filePath);
				} catch (error: unknown) {
					APIError.fromUnknownError(error).log();
				}
			}
		}
	}

	private readSizeOptions(width: number | undefined, height: number | undefined): ResizeOptions {
		return {
			...(width && { width: Number(width) }),
			...(height && { height: Number(height) })
		};
	}
}
