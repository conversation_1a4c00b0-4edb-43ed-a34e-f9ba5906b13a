import Joi from "joi";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	IPostVideoPayload,
	VideoDisplayModeEnum
} from "../../modules/interactiveVideo/interactiveVideo.interface";
import { CaptionData } from "../../modules/caption/caption.interface";

// Custom validation function for captionData JSON string
const validateCaptionData = (value: string, helpers: Joi.CustomHelpers) => {
	try {
		const captionData: CaptionData = JSON.parse(value);

		// Validate textColor if present
		if (captionData.textColor) {
			const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/;
			if (!hexColorRegex.test(captionData.textColor)) {
				return helpers.error("captionData.invalidTextColor");
			}
		}

		// Validate backgroundColor if present
		if (captionData.backgroundColor) {
			const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/;
			if (!hexColorRegex.test(captionData.backgroundColor)) {
				return helpers.error("captionData.invalidBackgroundColor");
			}
		}

		return value;
	} catch (error) {
		return helpers.error("captionData.invalidJSON");
	}
};

export const postVideoSchema = {
	data: Joi.object<IPostVideoPayload>({
		title: Joi.string().required(),
		description: Joi.string().allow("").max(150).optional(),
		videoId: Joi.string().hex().length(24).optional(),
		videoURL: Joi.string().optional(),
		productTitle0: Joi.string().optional(),
		productTitle1: Joi.string().optional(),
		productTitle2: Joi.string().optional(),
		productTitle3: Joi.string().optional(),
		productTitle4: Joi.string().optional(),
		productTitle5: Joi.string().optional(),
		productTitle6: Joi.string().optional(),
		productTitle7: Joi.string().optional(),
		productTitle8: Joi.string().optional(),
		productTitle9: Joi.string().optional(),
		productURL0: Joi.string().optional(),
		productURL1: Joi.string().optional(),
		productURL2: Joi.string().optional(),
		productURL3: Joi.string().optional(),
		productURL4: Joi.string().optional(),
		productURL5: Joi.string().optional(),
		productURL6: Joi.string().optional(),
		productURL7: Joi.string().optional(),
		productURL8: Joi.string().optional(),
		productURL9: Joi.string().optional(),
		productImageURL0: Joi.string().optional(),
		productImageURL1: Joi.string().optional(),
		productImageURL2: Joi.string().optional(),
		productImageURL3: Joi.string().optional(),
		productImageURL4: Joi.string().optional(),
		productImageURL5: Joi.string().optional(),
		productImageURL6: Joi.string().optional(),
		productImageURL7: Joi.string().optional(),
		productImageURL8: Joi.string().optional(),
		productImageURL9: Joi.string().optional(),
		productDescription0: Joi.string().optional(),
		productDescription1: Joi.string().optional(),
		productDescription2: Joi.string().optional(),
		productDescription3: Joi.string().optional(),
		productDescription4: Joi.string().optional(),
		productDescription5: Joi.string().optional(),
		productDescription6: Joi.string().optional(),
		productDescription7: Joi.string().optional(),
		productDescription8: Joi.string().optional(),
		productDescription9: Joi.string().optional(),
		subTitle0: Joi.string().optional(),
		subTitle1: Joi.string().optional(),
		subTitle2: Joi.string().optional(),
		subTitle3: Joi.string().optional(),
		subTitle4: Joi.string().optional(),
		subTitle5: Joi.string().optional(),
		subTitle6: Joi.string().optional(),
		subTitle7: Joi.string().optional(),
		subTitle8: Joi.string().optional(),
		subTitle9: Joi.string().optional(),
		ctaText: Joi.string().required(),
		showShopIcon: Joi.string().valid("true", "false").required(),
		showTitle: Joi.string().valid("true", "false").required(),
		videoPosterImageURL: Joi.string().required(),
		videoPosterPlayEmbedURL: Joi.string().required(),
		gifURL: Joi.string().required(),
		captionData: Joi.string().custom(validateCaptionData).messages({
			"captionData.invalidJSON": "Invalid JSON format for captionData",
			"captionData.invalidTextColor": "textColor must be a valid 24-bit (#RRGGBB) or 32-bit (#RRGGBBAA) hex color",
			"captionData.invalidBackgroundColor": "backgroundColor must be a valid 24-bit (#RRGGBB) or 32-bit (#RRGGBBAA) hex color"
		}),
		videoDisplayMode: Joi.string().valid(VideoDisplayModeEnum.PORTRAIT, VideoDisplayModeEnum.LANDSCAPE).optional(),
		phone: Joi.string().trim().optional(),
		email: Joi.string().lowercase().email({ tlds: { allow: false } }).trim().optional()
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT
	})
};
