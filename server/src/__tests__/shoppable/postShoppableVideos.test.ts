import supertest from "supertest";
import mongoose from "mongoose";
import { randomBytes } from "crypto";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { LocaleAPI } from "../../interfaces/apiTypes";
import TestHelper from "../mocks/testHelper";
import { ISignupEmailPayload } from "../../modules/signup/signup.interfaces";
import { createVideo } from "../../services/mongodb/video.service";
import { IVideo } from "../../modules/video/video.interfaces";

describe("POST /shoppable-videos", () => {
	let accessToken: string;
	let accountToken: string;
	let accountId: string;

	const expressApp = createServer();
	initExpressRoutes(expressApp);

	const testHelper = new TestHelper(expressApp);

	const signupEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "/"
	};

	beforeAll(async () => {
		({ accessToken } = await testHelper.signupEmail(signupEmailPayload));
		const account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();
		accountToken = await testHelper.getAccountToken(accountId, accessToken);
	});

	it("[OK - 201]. create shoppable-videos x-api-version < 4.", async () => {
		const res = await supertest(expressApp)
			.post("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoURL", "http://example.tld/example.mp4")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("videoPosterPlayEmbedURL", "http://example.tld/embed-example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("phone", "+***********")
			.field("email", "<EMAIL>")
			.field("ctaText", "Shop Now")
			.field("videoDisplayMode", "portrait")
			.field("showShopIcon", "true")
			.field("showTitle", "true");

		expect(res.statusCode).toBe(201);
	});

	it("[OK - 201]. create shoppable-videos x-api-version = 4. No product link", async () => {
		const video: IVideo = await createVideo(
			{
				accountId: accountId,
				videoWidthPx: 0,
				videoHeightPx: 0,
				publicVideoURL: "http://example.tld/example.mp4",
				videoProfile: new mongoose.Types.ObjectId(randomBytes(12).toString("hex"))
			},
			null
		);

		expect(video).toBeTruthy();
		expect(video._id).toBeTruthy();
		const videoId = video._id.toString();

		const shoppableVideoCreateData = {
			title: "abcd",
			ctaText: "Shop Now",
			videoDisplayMode: "portrait",
			showShopIcon: true,
			showTitle: true,
			videoPosterImageURL: "http://example.tld/example.jpg",
			gifURL: "http://example.tld/example.gif",
			phone: "+***********",
			email: "<EMAIL>",
			videoPosterPlayEmbedURL: "http://example.tld/embed-example.jpg"
		};

		const res = await supertest(expressApp)
			.post("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "4")
			.set("x-account-token", accountToken)
			.field("title", shoppableVideoCreateData.title)
			.field("videoId", videoId)
			.field("videoPosterImageURL", shoppableVideoCreateData.videoPosterImageURL)
			.field("gifURL", shoppableVideoCreateData.gifURL)
			.field("phone", shoppableVideoCreateData.phone)
			.field("email", shoppableVideoCreateData.email)
			.field("videoPosterPlayEmbedURL", shoppableVideoCreateData.videoPosterPlayEmbedURL)
			.field("ctaText", shoppableVideoCreateData.ctaText)
			.field("videoDisplayMode", shoppableVideoCreateData.videoDisplayMode)
			.field("showShopIcon", shoppableVideoCreateData.showShopIcon)
			.field("showTitle", shoppableVideoCreateData.showTitle);

		expect(res.statusCode).toBe(201);
		expect(res.body.shoppableVideo).toBeTruthy();
		const shoppableVideo = res.body.shoppableVideo;

		expect(shoppableVideo.accountId).toBe(accountId);
		expect(shoppableVideo.videoId).toBe(videoId);
		expect(shoppableVideo.title).toBe(shoppableVideoCreateData.title);
		expect(shoppableVideo.ctaText).toBe(shoppableVideoCreateData.ctaText);
		expect(shoppableVideo.videoDisplayMode).toBe(shoppableVideoCreateData.videoDisplayMode);
		expect(shoppableVideo.showShopIcon).toBe(shoppableVideoCreateData.showShopIcon);
		expect(shoppableVideo.showTitle).toBe(shoppableVideoCreateData.showTitle);
		expect(shoppableVideo.videoPosterURL).toBe(shoppableVideoCreateData.videoPosterImageURL);
		expect(shoppableVideo.gifURL).toBe(shoppableVideoCreateData.gifURL);
		expect(shoppableVideo.phone).toBe(shoppableVideoCreateData.phone);
		expect(shoppableVideo.email).toBe(shoppableVideoCreateData.email);
		expect(shoppableVideo.videoPosterPlayEmbedURL).toBe(shoppableVideoCreateData.videoPosterPlayEmbedURL);

		expect(shoppableVideo.products).toBeTruthy();
		expect(shoppableVideo.products.length).toBe(0);
	});


	it("[OK - 201]. create shoppable-videos x-api-version = 4. One product link", async () => {
		const video: IVideo = await createVideo(
			{
				accountId: accountId,
				videoWidthPx: 0,
				videoHeightPx: 0,
				publicVideoURL: "http://example.tld/example.mp4",
				videoProfile: new mongoose.Types.ObjectId(randomBytes(12).toString("hex"))
			},
			null
		);

		expect(video).toBeTruthy();
		expect(video._id).toBeTruthy();
		const videoId = video._id.toString();

		const shoppableVideoCreateData = {
			title: "This is great stuff",
			ctaText: "Shop Right Now",
			videoDisplayMode: "landscape",
			showShopIcon: true,
			showTitle: true,
			videoPosterImageURL: "http://example.tld/example.jpg",
			gifURL: "http://example.tld/example.gif",
			videoPosterPlayEmbedURL: "http://example.tld/embed-example.jpg"
		};

		const productLinkData = {
			productURL: "http://example.tld/example",
			productTitle: "product title",
			productDescription: "very nice product",
			subTitle: "$100",
			productImageURL: "http://example.tld/product-img.jpg"
		};

		const res = await supertest(expressApp)
			.post("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "4")
			.set("x-account-token", accountToken)
			.field("videoId", videoId)
			.field("title", shoppableVideoCreateData.title)
			.field("videoPosterImageURL", shoppableVideoCreateData.videoPosterImageURL)
			.field("gifURL", shoppableVideoCreateData.gifURL)
			.field("videoPosterPlayEmbedURL", shoppableVideoCreateData.videoPosterPlayEmbedURL)
			.field("ctaText", shoppableVideoCreateData.ctaText)
			.field("videoDisplayMode", shoppableVideoCreateData.videoDisplayMode)
			.field("showShopIcon", shoppableVideoCreateData.showShopIcon)
			.field("showTitle", shoppableVideoCreateData.showTitle)
			.field("productURL0", productLinkData.productURL)
			.field("productTitle0", productLinkData.productTitle)
			.field("productDescription0", productLinkData.productDescription)
			.field("subTitle0", productLinkData.subTitle)
			.field("productImageURL0", productLinkData.productImageURL);

		expect(res.statusCode).toBe(201);
		expect(res.body.shoppableVideo).toBeTruthy();
		const shoppableVideo = res.body.shoppableVideo;

		expect(shoppableVideo.products).toBeTruthy();
		expect(shoppableVideo.products.length).toBe(1);

		const productLink = shoppableVideo.products[0];
		expect(productLink.url).toBe(productLinkData.productURL);
		expect(productLink.title).toBe(productLinkData.productTitle);
		expect(productLink.productDescription).toBe(productLinkData.productDescription);
		expect(productLink.subTitle).toBe(productLinkData.subTitle);
		expect(productLink.productThumbnail).toBe(productLinkData.productImageURL);
	});

	it("[OK - 201]. Should create shoppable video with valid 24-bit hex colors in captionData", async () => {
		const validCaptionData = JSON.stringify({
			enabled: true,
			captionText: [],
			textColor: "#FFFFFF", // Valid 24-bit hex color
			backgroundColor: "#000000", // Valid 24-bit hex color
			fontSizePx: 22,
			xPos: 0,
			yPos: 10000
		});

		const res = await supertest(expressApp)
			.post("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoURL", "http://example.tld/example.mp4")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("videoPosterPlayEmbedURL", "http://example.tld/example-play.jpg")
			.field("ctaText", "Shop Now")
			.field("showShopIcon", "true")
			.field("showTitle", "true")
			.field("videoDisplayMode", "portrait")
			.field("captionData", validCaptionData);

		expect(res.statusCode).toBe(201);
		expect(res.body.shoppableVideo).toBeTruthy();
		expect(res.body.shoppableVideo.captionData).toBeTruthy();
		expect(res.body.shoppableVideo.captionData.textColor).toBe("#FFFFFF");
		expect(res.body.shoppableVideo.captionData.backgroundColor).toBe("#000000");
	});

	it("[OK - 201]. Should create shoppable video with valid 32-bit hex colors in captionData", async () => {
		const validCaptionData = JSON.stringify({
			enabled: true,
			captionText: [],
			textColor: "#FFFFFF80", // Valid 32-bit hex color with alpha
			backgroundColor: "#********", // Valid 32-bit hex color with alpha
			fontSizePx: 22,
			xPos: 0,
			yPos: 10000
		});

		const res = await supertest(expressApp)
			.post("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoURL", "http://example.tld/example.mp4")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("videoPosterPlayEmbedURL", "http://example.tld/example-play.jpg")
			.field("ctaText", "Shop Now")
			.field("showShopIcon", "true")
			.field("showTitle", "true")
			.field("videoDisplayMode", "portrait")
			.field("captionData", validCaptionData);

		expect(res.statusCode).toBe(201);
		expect(res.body.shoppableVideo).toBeTruthy();
		expect(res.body.shoppableVideo.captionData).toBeTruthy();
		expect(res.body.shoppableVideo.captionData.textColor).toBe("#FFFFFF80");
		expect(res.body.shoppableVideo.captionData.backgroundColor).toBe("#********");
	});
});
