import React from "react";
import { AccountSubscription } from "@src/types/account";
import { CaptionData } from "./caption";

export enum SnippetOptions {
	CAROUSEL = "carousel",
	WIDGET = "widget",
	ONSITE = "onsite"
}

export enum SnippetDisplayMode {
	SINGLE = "single",
	SM_DOUBLE = "sm_double",
	DOUBLE = "double",
	ALL = "all"
}

export enum DisplayFormatOptions {
	PORTRAIT = "portrait",
	LANDSCAPE = "landscape"
}

export const fontOptions = [
	{ label: "Readex Pro (Default)", value: "Readex Pro" },
	{ label: "Roboto", value: "Roboto" },
	{ label: "<PERSON>", value: "<PERSON>" },
	{ label: "Playfair Display", value: "Playfair Display" },
	{ label: "Merriweather", value: "Merriweather" },
	{ label: "Bebas neue", value: "Bebas neue" },
	{ label: "<PERSON><PERSON><PERSON>", value: "<PERSON><PERSON><PERSON>" },
	{ label: "Fira Sans Condensed", value: "Fira Sans Condensed" },
	{ label: "Arvo", value: "Arvo" },
	{ label: "IBM Plex Mono", value: "IBM Plex Mono" },
	{ label: "Varela Round", value: "Varela Round" },
	{ label: "Alfa Slab One", value: "Alfa Slab One" }
];

export const platformOptions = [
	{
		label: "Select platform",
		value: ""
	},
	{
		label: "Shopify",
		value: "Shopify"
	},
	{
		label: "Squarespace",
		value: "Squarespace"
	},
	{
		label: "Wix",
		value: "Wix"
	},
	{
		label: "Wordpress",
		value: "Wordpress"
	},
	{
		label: "Webflow",
		value: "Webflow"
	},
	{
		label: "BigCommerce",
		value: "BigCommerce"
	},
	{
		label: "I Don't know",
		value: "I Don't know"
	},
	{
		label: "Other",
		value: "Other"
	}
];

export const teamOptions = [
	{
		label: "Select team",
		value: ""
	},
	{
		label: "Marketing / Social",
		value: "Marketing / Social"
	},
	{
		label: "Product / Design",
		value: "Product / Design"
	},
	{
		label: "Creative Production",
		value: "Creative Production"
	},
	{
		label: "Engineering / IT",
		value: "Engineering / IT"
	},
	{
		label: "Customer Service",
		value: "Customer Service"
	},
	{
		label: "Owner / Founder",
		value: "Owner / Founder"
	},
	{
		label: "Operations",
		value: "Operations"
	},
	{
		label: "Finance",
		value: "Finance"
	},
	{
		label: "HR / Legal",
		value: "HR / Legal"
	},
	{
		label: "Other",
		value: "Other"
	}
];

export interface VideoPropsProduct {
	productTitle: string;
	productURL: string;
	imagePreview: string;
	subTitle: string;
}

export interface PostMessageAccount {
	allowLandscape: boolean | undefined;
	allowThemes: boolean | undefined;
	hideVanityBranding: boolean | undefined;
	subscriptionType: string | undefined;
}

export interface PostMessageProduct {
	title: string;
	url: string;
	productThumbnail: string;
	subTitle: string;
}

export interface PostMessageVideo {
	title: string;
	videoURL: string;
	videoPosterURL: string;
	ctaText: string;
	showShopIcon: boolean;
	showTitle: boolean;
	products: PostMessageProduct[];
	videoDisplayMode: DisplayFormatOptions;
}

export interface PostMessageObject {
	type: string;
	data: {
		video: PostMessageVideo;
		account: PostMessageAccount;
	};
}

export interface VideoPreviewProps {
	subscription: AccountSubscription | undefined;
	appEndpoint: string;
	title: string;
	videoURL: string;
	videoPosterURL: string;
	showTitle: boolean;
	emailAddress?: string;
	phoneNumber?: string;
	products: VideoPropsProduct[];
	videoDisplayMode: DisplayFormatOptions;
	children?: React.ReactNode;
	captionData?: CaptionData;
}
