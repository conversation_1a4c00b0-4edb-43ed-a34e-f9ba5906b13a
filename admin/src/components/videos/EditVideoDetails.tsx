/* eslint-disable max-lines */
import React, {
	useRef,
	useState,
	useEffect
} from "react";
import {
	Form,
	Row,
	Col
} from "react-bootstrap";
import {
	useParams,
	useNavigate
} from "react-router-dom";
import Skeleton from "react-loading-skeleton";
import Spinner from "react-bootstrap/Spinner";
import {
	FlexSpace,
	CustomSwitch,
	PageBody,
	MainButton,
	MainSuccessButton,
	VideoPageTitleText,
	FileDropBox,
	CoverImageBox,
	NoImageFileBox,
	UploadImage,
	ProductsBox,
	ProductImage,
	UploadProductImage,
	CustomInputBox,
	FetchingProductText,
	SortIcon,
	VideoPerformance,
	VideoPerformanceSection,
	VideoMetricTitle,
	FlexCol,
	Flex,
	VideoPercentageSubheading,
	InfoBox,
	StarIcon,
	VideoWrapper,
	BlurredLock,
	FlexSwitchRow,
	FlexSwitchCol,
	PortraitBox,
	LandscapeBox,
	ImageButton,
	VideoCaptionsText
} from "@src/styles/components";
import {
	HeadingText,
	ConfirmationBox,
	ConfirmationBoxWrapper,
	BodyText,
	CustomInput,
	CustomTextarea,
	CircleXmarkIcon,
	ImageIcon
} from "@src/styles/forms";
import { useTranslation } from "../hooks/translations";
import { getErrorString } from "../hooks/getErrorString";
import getVideo from "../hooks/getVideo";
import updateVideo from "../hooks/updateVideo";
import { useTokenCheck } from "../hooks/useTokenCheck";
import {
	FileInput,
	shoppableVideoData,
	Products,
	companyDetails
} from "@src/types/videos";
import parseProductUrl from "../hooks/parseProductUrl";
import {
	UploadIcon,
	Plays,
	OpenLockIcon,
	Likes,
	Clicks,
	HelpIconGrey,
	EngagementScore,
	LinkIconBlack,
	EmailIconBlack,
	PhoneIconBlack
} from "@src/assets";
import {
	CustomSelectItem,
	CustomSelectOption,
	CustomSelectStyles
} from "../inputs/CustomSelect";
import Select, {
	components,
	OnChangeValue
} from "react-select";
import getCollections from "../hooks/getCollections";
import { ShoppableCollection } from "@src/types/collections";
import updateCollection from "../hooks/updateCollection";
import {
	arraysAreEqualIgnoreOrder,
	getImageSize
} from "@src/utils/compare";
import { CoverImageModal } from "../modals/CoverImageModal";
import { ConfirmLeavingModal } from "../modals/ConfirmLeavingModal";
import { secondsToFormattedTime } from "../../utils/time";
import Scorebar from "../metrics/Scorebar";
import BarGraph from "../metrics/BarGraph";
import { registerEvent } from "@src/components/events/service";
import { EventNameEnum } from "@src/types/events";
import { usePasswordRequired } from "../hooks/getPasswordRequired";
import { EnterPasswordModal } from "../modals/EnterPasswordModal";
import addCollection from "../hooks/addCollection";
import optimizeImage from "../hooks/optimizeImage";
import uploadFile from "../hooks/uploadFile";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import VideoPreview from "./VideoPreview";
import {
	DisplayFormatOptions,
	VideoPropsProduct
} from "@src/types/snippetOptions";
import { AccountSubscription } from "@src/types/account";
import { VideoUsageModal } from "../modals/VideoUsageModal";
import { ProductTypeEnum } from "../../types/product";
import { ShareModal } from "../modals/ShareModal";
import {
	ShareObject,
	ShareType
} from "@src/types/share";
import deepEqual from "deep-equal";
import { useMask } from "@react-input/mask";
import DetailShareButton from "../share/DetailShareButton";
import { validateVideoFields } from "@src/utils/validation";
import { CaptionsModal } from "../modals/CaptionsModal";
import { CaptionData } from "@src/types/caption";

interface Props {
	userData?: companyDetails | undefined;
	subscription?: AccountSubscription | undefined;
	saveChanges: boolean;
	setSaveChanges: (value: boolean) => void;
	navigationUrl: string;
	setNavigationUrl: (value: string) => void;
	modalStatus: boolean;
	setModalStatus: (value: boolean) => void;
	hideCreateVideo: boolean;
}

// eslint-disable-next-line max-lines-per-function
const EditVideoDetails: React.FC<Props> = ({
	userData,
	subscription,
	saveChanges,
	setSaveChanges,
	navigationUrl,
	setNavigationUrl,
	modalStatus,
	setModalStatus,
	hideCreateVideo
}) => {
	const { id } = useParams<{id: string}>();
	const navigate = useNavigate();
	const translation = useTranslation();
	const { apiRetryHandler } = useTokenCheck();
	const [editVideoError, setEditVideoError] = useState("");
	const [loading, setLoading] = useState(false);
	const [videoData, setVideoData] = useState<shoppableVideoData>();
	const [videoDataCopy, setVideoDataCopy] = useState<shoppableVideoData>({} as shoppableVideoData);
	const [refetch, setRefetch] = useState(true);
	const [generateButton, setGenerateButton] = useState(true);
	const [uploadFileURL, setUploadFileURL] = useState("");
	const [videoTitleValue, setVideoTitleValue] = useState("");
	const [videoDescValue, setVideoDescValue] = useState("");
	const [videoDescriptionCount, setVideoDescriptionCount] = useState(0);
	const [videoButtonTextValue, setVideoButtonTextValue] = useState("");
	const [confirmationText, setConfirmationText] = useState("");
	const [coverImage, setCoverImage] = useState<File>();
	const [coverImageURL, setCoverImageURL] = useState("");
	const [gifURL, setGifURL] = useState("");
	const [videoPosterPlayEmbedURL, setVideoPosterPlayEmbedURL] = useState("");
	const [imagePreview, setImagePreview] = useState("");
	const [emailAddress, setEmailAddress] = useState("");
	const [emailAddressCopy, setEmailAddressCopy] = useState("");
	const [showEmailAddress, setShowEmailAddress] = useState(false);
	const [phoneNumber, setPhoneNumber] = useState("");
	const [phoneNumberCopy, setPhoneNumberCopy] = useState("");
	const [showPhoneNumber, setShowPhoneNumber] = useState(false);
	const emailRef = useRef<HTMLInputElement>(null);
	const phoneRef = useMask({ mask: "(___) ___-____", replacement: { _: /\d/ } });
	const [productsInputs, setProductsInputs] = useState<FileInput[]>([]);
	const [allCollections, setAllCollections] = useState<ShoppableCollection[]>([]);
	const [defaultCollection, setDefaultCollection] = useState<ShoppableCollection>();
	const [unselectedCollections, setUnselectedCollections] = useState<CustomSelectOption[]>([]);
	const [initialSelectedCollections, setInitialSelectedCollections] = useState<ShoppableCollection[]>([]);
	const [selectedCollections, setSelectedCollections] = useState<ShoppableCollection[]>([]);
	const [newCollectionSelected, setNewCollectionSelected] = useState(false);
	const [coverImageStatus, setCoverImageStatus] = useState(false);
	const [displayTitle, setDisplayTitle] = useState(true);
	const [displayShopIcon, setDisplayShopIcon] = useState(true);
	const [displayTitleCopy, setDisplayTitleCopy] = useState(true);
	const [displayShopIconCopy, setDisplayShopIconCopy] = useState(true);
	const [goToPrevPage, setGoToPrevPage] = useState(false);
	const [displayEngagementScoreInfo, setDisplayEngagementScoreInfo] = useState(false);
	const [enterPassword, setEnterPassword] = useState(false);
	const passwordRequired = usePasswordRequired();
	const [displayFormat, setDisplayFormat] = useState(DisplayFormatOptions.PORTRAIT);
	const [displayFormatCopy, setDisplayFormatCopy] = useState(DisplayFormatOptions.PORTRAIT);
	const [videoWidthPx, setVideoWidthPx] = useState(500);
	const [videoHeightPx, setVideoHeightPx] = useState(750);
	const [videoModalStatus, setVideoModalStatus] = useState(false);
	const [shareModalStatus, setShareModalStatus] = useState(false);
	const [shareObject, setShareObject] = useState<ShareObject>();
	const [captionsModalStatus, setCaptionsModalStatus] = useState(false);
	const [captionData, setCaptionData] = useState<CaptionData | undefined>();
	let interactionsCount = 1;

	const updateProductsList = (list: FileInput[]) => {
		const tempArray: Products[] = [];
		list?.map((x) => {
			tempArray.push({
				_id: x?._id,
				title: x?.productTitle,
				url: x?.productURL,
				productThumbnail: x?.imagePreview,
				subTitle: x?.subTitle ?? undefined
			});
		});

		setVideoDataCopy({
			...videoDataCopy,
			shoppableVideo: {
				...videoDataCopy.shoppableVideo,
				products: tempArray
			}
		});
	};

	const handleRemoveClick = (index: number) => {
		const list = [...productsInputs];
		list.splice(index, 1);
		setProductsInputs(list);

		updateProductsList(list);
	};

	const handleAddClick = () => {
		const newInput = {
			productImageFile: undefined,
			imagePreview: "",
			productTitle: "",
			productURL: "",
			subTitle: undefined,
			hovering: false
		} as FileInput;
		const list = [...productsInputs];
		list.push(newInput);
		setProductsInputs(list);

		updateProductsList(list);
	};

	const handleInputChange = (value: string, typeInput: string, index: number) => {
		const list = [...productsInputs];
		if (typeInput === "productTitle") list[index].productTitle = value;
		if (typeInput === "subTitle") list[index].subTitle = value;
		if (typeInput === "productURL") {
			list[index].productURL = value;
			if (!value) list[index].showProduct = false;
		}
		setProductsInputs(list);

		updateProductsList(list);
	};

	const handleMouseOver = (index: number) => {
		const list = [...productsInputs];
		list[index].hovering = true;
		setProductsInputs(list);
	};

	const handleMouseLeave = (index: number) => {
		const list = [...productsInputs];
		list[index].hovering = false;
		setProductsInputs(list);
	};

	const handleParseProductUrl = async (value: string, index: number) => {
		const list = [...productsInputs];
		const productURL = list[index].productURL;
		if (productURL) {
			if (!productURL.includes("https://")) list[index].productURL = `https://${productURL}`;
			list[index].fetchingProduct = true;
			list[index].showProduct = true;
			setProductsInputs(list);
			const { data } = await apiRetryHandler(async () => await parseProductUrl(value));
			if (data) {
				const list = [...productsInputs];
				list[index].productTitle = data.title;
				list[index].subTitle = data.price ?? undefined;
				const { data: optimizedImage, error: jobError } = await apiRetryHandler(
					async () => await optimizeImage(data.imageURL, 500)
				);
				if (jobError) {
					list[index].imagePreview = "";
					list[index].imageURL = "";
				} else {
					list[index].imagePreview = optimizedImage;
					list[index].imageURL = optimizedImage;
				}
				list[index].fetchingProduct = false;
				setProductsInputs(list);
			}
		} else {
			list[index].productTitle = "";
			list[index].imagePreview = "";
			list[index].imageURL = "";
			list[index].subTitle = undefined;
			setProductsInputs(list);
		}
	};

	const handleTitleChange = (value: string) => {
		setVideoTitleValue(value);
		setVideoDataCopy({
			...videoDataCopy,
			shoppableVideo: {
				...videoDataCopy.shoppableVideo,
				title: value
			}
		});
	};
	const handleDescChange = (value: string) => {
		setVideoDescValue(value);
		setVideoDescriptionCount(value.length);
		setVideoDataCopy({
			...videoDataCopy,
			shoppableVideo: {
				...videoDataCopy.shoppableVideo,
				description: value
			}
		});
	};

	const updateShoppableVideo = async (password?: string) => {
		if (showEmailAddress && emailRef.current) {
			const emailInput = emailRef.current;
			const emailPattern = /[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,20}$/i;

			if (!emailPattern.test(emailInput.value)) {
				emailInput.classList.add("is-invalid");
				setEditVideoError(translation.errors.emailFormat);
				return;
			}

			emailInput.classList.remove("is-invalid");
		}

		if (showPhoneNumber && phoneRef.current) {
			const phoneInput = phoneRef.current;
			const phonePattern = /^\(\d{3}\) \d{3}-\d{4}$/;

			if (!phonePattern.test(phoneInput.value)) {
				phoneInput.classList.add("is-invalid");
				setEditVideoError(translation.errors.phoneFormat);
				return;
			}

			phoneInput.classList.remove("is-invalid");
		}

		setGenerateButton(true);
		setLoading(true);
		if (id) {
			let coverImageLink = coverImageURL;
			if (coverImage) {
				const { data: uploadData, error: uploadError } = await apiRetryHandler(
					async () => await uploadFile(coverImage as File)
				);
				if (!uploadError) {
					const { imageWidthPx, imageHeightPx } = getImageSize(videoWidthPx, videoHeightPx);
					const { data: optimizedImage, error: jobError } = await apiRetryHandler(
						async () => await optimizeImage(uploadData.publicURL, imageWidthPx, imageHeightPx)
					);
					if (!jobError) {
						coverImageLink = optimizedImage;
					} else {
						setGenerateButton(false);
						setLoading(false);
						const errorText = getErrorString(translation, jobError?.message);
						setEditVideoError(errorText);
						return;
					}
				}
			}

			const { data, error } = await apiRetryHandler(
				async () =>
					await updateVideo({
						id,
						videoTitleValue,
						videoDescValue,
						uploadFileURL,
						coverImageLink,
						gifURL,
						videoPosterPlayEmbedURL,
						videoButtonTextValue,
						displayFormat,
						productsInputs,
						displayTitle,
						displayShopIcon,
						emailAddress,
						phoneNumber,
						password,
						captionData
					})
			);
			if (error) {
				setGenerateButton(false);
				setLoading(false);
				const errorText = getErrorString(translation, error?.response?.data?.error);
				setEditVideoError(errorText);
				return;
			}

			for (const selectedCollection of selectedCollections) {
				if (!selectedCollection.shoppableVideos?.includes(id)) selectedCollection.shoppableVideos?.push(id);
				const { error: updateCollectionError } = await apiRetryHandler(
					async () => await updateCollection(selectedCollection._id, selectedCollection.shoppableVideos ?? [], password)
				);
				if (updateCollectionError) {
					setGenerateButton(false);
					setLoading(false);
					const errorText = getErrorString(translation, updateCollectionError?.response?.data?.error);
					setEditVideoError(errorText);
					return;
				}
			}

			if (newCollectionSelected) {
				const { error: createCollectionError } = await apiRetryHandler(
					async () =>
						await addCollection({ collectionTitle: data.shoppableVideo.title, videos: [data.shoppableVideo], password })
				);
				if (createCollectionError) {
					setGenerateButton(false);
					setLoading(false);
					const errorText = getErrorString(translation, createCollectionError?.response?.data?.error);
					setEditVideoError(errorText);
					return;
				}
			}

			const removedCollections = initialSelectedCollections
				.filter((collection) =>
					unselectedCollections.some((unselected: CustomSelectOption) => unselected.value === collection._id)
				)
				.map((collection) => {
					return { ...collection, shoppableVideos: collection.shoppableVideos?.filter((videoId) => videoId !== id) };
				});

			for (const removedCollection of removedCollections) {
				const { error: updateCollectionError } = await apiRetryHandler(
					async () => await updateCollection(removedCollection._id, removedCollection.shoppableVideos ?? [])
				);
				if (updateCollectionError) {
					setGenerateButton(false);
					setLoading(false);
					const errorText = getErrorString(translation, updateCollectionError?.response?.data?.error);
					setEditVideoError(errorText);
					return;
				}
			}
		}
		setEditVideoError("");
		setConfirmationText(translation.editVideoPage.dataUpdated);
		setTimeout(() => {
			setConfirmationText("");
			setRefetch(true);
			setLoading(false);
			setEnterPassword(false);
			setNewCollectionSelected(false);
		}, 1000);
	};


	const validateFields = () => {
		const validationError = validateVideoFields(
			videoTitleValue,
			uploadFileURL,
			translation,
			productsInputs
		);

		if (validationError) {
			setEditVideoError(validationError);
		}
	};

	const handleSave = () => {
		if (passwordRequired) {
			setEnterPassword(true);
		} else {
			updateShoppableVideo();
		}
	};

	// Product images upload
	const onDrop = async (event: React.FormEvent<HTMLLabelElement>, index: number) => {
		const reader = new FileReader();
		const file = (event.target as HTMLInputElement)?.files?.[0];
		if (file) {
			const list = [...productsInputs];
			reader.onloadend = async () => {
				let imageLink = "";
				const { data: uploadData, error: uploadError } = await apiRetryHandler(
					async () => await uploadFile(file as File)
				);
				if (!uploadError) {
					const { data: optimizedImage, error: jobError } = await apiRetryHandler(
						async () => await optimizeImage(uploadData.publicURL, 500)
					);
					if (!jobError) {
						imageLink = optimizedImage;
					}
				}
				list[index].imageURL = imageLink;
				list[index].imagePreview = imageLink;
				setProductsInputs(list);
				updateProductsList(list);
			};
			reader.readAsDataURL(file);
		}
	};

	const ThemeChanged = !deepEqual(videoData, videoDataCopy);

	useEffect(() => {
		const invalidLink = productsInputs.some((i) => i.productURL && !i.productTitle);
		const selectedCollectionIds = selectedCollections.map((collection) => collection._id);
		const initialSelectedCollectionIds = initialSelectedCollections.map((collection) => collection._id);

		if (
			(videoTitleValue && uploadFileURL && !invalidLink && ThemeChanged && videoButtonTextValue) ||
			displayFormat !== displayFormatCopy ||
			displayTitle !== displayTitleCopy ||
			emailAddress !== emailAddressCopy ||
			phoneNumber !== phoneNumberCopy ||
			displayShopIcon !== displayShopIconCopy ||
			!arraysAreEqualIgnoreOrder(selectedCollectionIds, initialSelectedCollectionIds) ||
			newCollectionSelected
		) {
			setGenerateButton(false);
			setSaveChanges(true);
		} else {
			setGenerateButton(true);
			setSaveChanges(false);
		}
	}, [
		videoTitleValue,
		initialSelectedCollections,
		coverImage,
		coverImageURL,
		uploadFileURL,
		productsInputs,
		videoButtonTextValue,
		ThemeChanged,
		selectedCollections,
		unselectedCollections,
		setSaveChanges,
		displayTitle,
		displayTitleCopy,
		displayShopIcon,
		displayShopIconCopy,
		emailAddress,
		emailAddressCopy,
		phoneNumber,
		phoneNumberCopy,
		newCollectionSelected,
		displayFormat,
		displayFormatCopy
	]);

	useEffect(() => {
		const go = async () => {
			if (id) {
				const { data, error } = await apiRetryHandler(async () => await getVideo(id));

				if (error) {
					setEditVideoError(translation.errors.internalError);
				} else {
					setVideoData(data as shoppableVideoData);
					setVideoDataCopy(data as shoppableVideoData);
					setVideoTitleValue(data?.shoppableVideo?.title);
					setVideoDescValue(data?.shoppableVideo?.description);
					if (data?.shoppableVideo?.description) {
						setVideoDescriptionCount(data?.shoppableVideo?.description.length);
					}
					setUploadFileURL(data?.shoppableVideo?.videoURL);
					setImagePreview(data?.shoppableVideo?.videoPosterURL);
					setCoverImageURL(data?.shoppableVideo?.videoPosterURL);
					setGifURL(data?.shoppableVideo?.gifURL);
					setCaptionData(data?.shoppableVideo?.captionData);
					setVideoPosterPlayEmbedURL(data?.shoppableVideo?.videoPosterPlayEmbedURL);
					setVideoButtonTextValue(data?.shoppableVideo?.ctaText ?? translation.editVideoPage.videoButtonText);
					setDisplayTitle(data?.shoppableVideo?.showTitle);
					setDisplayShopIcon(data?.shoppableVideo?.showShopIcon);
					setDisplayTitleCopy(data?.shoppableVideo?.showTitle);
					setDisplayShopIconCopy(data?.shoppableVideo?.showShopIcon);

					if (data?.shoppableVideo?.videoDisplayMode) {
						setDisplayFormat(data.shoppableVideo.videoDisplayMode);
						setDisplayFormatCopy(data.shoppableVideo.videoDisplayMode);
					}

					if (data?.shoppableVideo?.videoWidthPx && data?.shoppableVideo?.videoHeightPx) {
						setVideoWidthPx(data.shoppableVideo.videoWidthPx);
						setVideoHeightPx(data.shoppableVideo.videoHeightPx);
					}

					if (data?.shoppableVideo?.email && subscription?.allowCTALead) {
						setShowEmailAddress(true);
						setEmailAddress(data.shoppableVideo.email);
						setEmailAddressCopy(data.shoppableVideo.email);
					}

					if (data?.shoppableVideo?.phone && subscription?.allowCTALead) {
						const phone = formatPhoneNumber(data.shoppableVideo.phone);

						setShowPhoneNumber(true);
						setPhoneNumber(phone);
						setPhoneNumberCopy(phone);
					}

					if (data?.shoppableVideo?.title && data?.shoppableVideo?.gifURL) {
						setShareObject({
							videoId: id,
							type: ShareType.SHARE_VIDEO,
							videoTitle: data.shoppableVideo.title,
							gifURL: data.shoppableVideo.gifURL
						});
					}

					const list: FileInput[] = [];
					data?.shoppableVideo?.products.map((x: Products) => {
						list.push({
							productImageFile: undefined,
							imagePreview: x?.productThumbnail ?? "",
							productTitle: x?.title ?? "",
							productURL: x?.url ?? "",
							showProduct: true,
							hovering: false,
							subTitle: x?.subTitle ?? undefined
						});
					});
					setProductsInputs(list);
				}

				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				const { data: collectionsData, error: collectionsError }: {data: ShoppableCollection[]; error: any} =
					await apiRetryHandler(async () => await getCollections());
				if (collectionsError) {
					const errorText = getErrorString(translation, collectionsError?.response?.data?.error);
					setEditVideoError(errorText);
				} else if (collectionsData.length) {
					setAllCollections(collectionsData);
					setDefaultCollection(collectionsData[0]);

					const selected = collectionsData.filter(
						(collection) => collection._id !== collectionsData[0]._id && collection.shoppableVideos?.includes(id)
					);
					selected.sort((a, b) => {
						if (a.title && b.title) {
							return a.title.localeCompare(b.title);
						} else if (a.title && !b.title) {
							return -1;
						} else if (!a.title && b.title) {
							return 1;
						}
						return 0;
					});

					setSelectedCollections(selected);
					setInitialSelectedCollections(selected);
					setUnselectedCollections([
						...collectionsData
							.filter(
								(collection) => collection._id !== collectionsData[0]._id && !collection.shoppableVideos?.includes(id)
							)
							.map((collection) => {
								return { value: collection._id, label: collection.title };
							}),
						{ value: "new_collection", label: translation.createVideoPage.createCollectionText }
					]);
				}

				setRefetch(false);
			} else {
				navigate("/");
			}
		};

		if (refetch) {
			go();
		}
	}, [
		setVideoData,
		captionData,
		setVideoTitleValue,
		setVideoButtonTextValue,
		translation,
		setUnselectedCollections,
		setSelectedCollections,
		setInitialSelectedCollections,
		refetch,
		apiRetryHandler,
		id,
		navigate,
		subscription,
		translation.editVideoPage.videoButtonText
	]);

	const formatPhoneNumber = (phone: string) => {
		const cleaned = phone.replace(/\D/g, "");
		const match = cleaned.match(/^(1?)(\d{3})(\d{3})(\d{4})$/);
		return match ? `(${match[2]}) ${match[3]}-${match[4]}` : phone;
	};

	const handleCollectionSelect = (selectedOption: OnChangeValue<CustomSelectOption, false>) => {
		const selectedCollection = allCollections.find((collection) => collection._id === selectedOption?.value);
		if (selectedCollection) {
			const insertIndex = selectedCollections.findIndex(
				(collection) =>
					collection.title !== undefined &&
					selectedCollection.title !== undefined &&
					collection.title.localeCompare(selectedCollection.title) > 0
			);

			let newSelectedCollections;
			if (insertIndex !== -1) {
				newSelectedCollections = [
					...selectedCollections.slice(0, insertIndex),
					selectedCollection,
					...selectedCollections.slice(insertIndex)
				];
			} else {
				newSelectedCollections = [...selectedCollections, selectedCollection];
			}
			setSelectedCollections(newSelectedCollections);
			setUnselectedCollections(
				unselectedCollections.filter((collection: CustomSelectOption) => collection.value !== selectedCollection._id)
			);
		} else if (selectedOption?.value === "new_collection") {
			setUnselectedCollections(
				unselectedCollections.filter((collection: CustomSelectOption) => collection.value !== "new_collection")
			);
			setNewCollectionSelected(true);
		}
	};

	const handleCollectionRemove = (_id: string) => {
		const matchingCollection = allCollections.find((collection) => collection._id === _id);
		if (matchingCollection) {
			setSelectedCollections(selectedCollections.filter((collection) => collection._id !== _id));
			let unselectedCols;
			if (unselectedCollections[unselectedCollections.length - 1].value === "new_collection") {
				unselectedCols = [
					...unselectedCollections.filter((col) => col.value !== "new_collection"),
					{ value: matchingCollection._id, label: matchingCollection.title },
					{ value: "new_collection", label: translation.createVideoPage.createCollectionText }
				];
			} else {
				unselectedCols = [...unselectedCollections, { value: matchingCollection._id, label: matchingCollection.title }];
			}
			setUnselectedCollections(unselectedCols);
		}
	};

	const handleNewCollectionRemove = () => {
		setNewCollectionSelected(false);
		setUnselectedCollections([
			...unselectedCollections,
			{ value: "new_collection", label: translation.createVideoPage.createCollectionText }
		]);
	};

	const handlePlanClick = () => {
		if (saveChanges) {
			setNavigationUrl("/plans-pricing");
			setModalStatus(true);
		} else {
			navigate("/plans-pricing");
		}
	};

	const navigateToCreateVideo = () => {
		registerEvent({
			eventName: EventNameEnum.CREATE_VIDEO_PRESS
		});
		navigate("/create-video");
	};

	const toggleCaptionsEnabled = () => {
		if (!captionData || !videoDataCopy) return;

		const updated = { ...captionData, enabled: !captionData.enabled };
		setCaptionData(updated);
		setVideoDataCopy({
			...videoDataCopy,
			shoppableVideo: {
				...videoDataCopy.shoppableVideo,
				captionData: updated
			}
		});
	};

	useEffect(() => {
		// Prevent navigation if changes are not saved
		if (saveChanges) window.history.pushState(null, "", window.location.pathname);

		// prompt before refresh page
		const handleBeforeUnload = (event: {preventDefault: () => void; returnValue: string}) => {
			if (saveChanges) {
				event.preventDefault();
				// Required for some browsers
				event.returnValue = "";
			}
		};

		const handlePopState = () => {
			if (saveChanges) {
				window.history.pushState(null, "", window.location.pathname);
				setModalStatus(true);
				setGoToPrevPage(true);
			}
		};

		window.addEventListener("beforeunload", handleBeforeUnload);
		window.addEventListener("popstate", handlePopState);

		return () => {
			window.removeEventListener("beforeunload", handleBeforeUnload);
			window.removeEventListener("popstate", handlePopState);
		};
	}, [saveChanges, setModalStatus]);

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const CustomSingleValue: React.FC<any> = ({ ...props }) => (
		<components.SingleValue {...props}>
			{translation.editVideoPage.addToCollectionText}
			<SortIcon />
		</components.SingleValue>
	);

	return (
		<>
			<ConfirmationBoxWrapper>
				{!!confirmationText && (
					<ConfirmationBox className="text-center" data-testid="deleteConfirmation">
						{confirmationText}
					</ConfirmationBox>
				)}
				{!!editVideoError && (
					<ErrorMessage error={editVideoError} setError={setEditVideoError} displayCloseIcon={true} />
				)}
			</ConfirmationBoxWrapper>
			<PageBody>
				{/* desktop view */}
				<Row className="mb-4 d-none d-lg-flex">
					<Col sm="12" md="12" lg="12" xl="6" className="mb-4">
						<HeadingText data-testid="editVideoPage">{translation.editVideoPage.shoppableVideo}</HeadingText>
					</Col>
					<Col sm="12" md="12" lg="12" xl="6" className="mb-4 text-end">
						<DetailShareButton
							allowSharing={subscription?.allowSharing === true}
							disabled={false}
							setShareModalStatus={setShareModalStatus}
							shareType={ShareType.SHARE_VIDEO}
						/>

						{generateButton && !loading && (
							<MainSuccessButton
								type="button"
								data-testid="addAnotherVideo"
								onClick={() => {
									if (hideCreateVideo) {
										setVideoModalStatus(true);
									} else {
										navigateToCreateVideo();
									}
								}}
								className="ms-3"
							>
								{translation.editVideoPage.addAnotherVideo}
							</MainSuccessButton>
						)}

						<MainButton
							type="button"
							data-testid="updateButton"
							inactive={generateButton}
							onClick={generateButton ? validateFields : handleSave}
							className="ms-3"
						>
							{translation.general.update}
							{loading ? (
								<Spinner as="span" size="sm" role="status" aria-hidden="true" style={{ marginLeft: "1rem" }} />
							) : (
								""
							)}
						</MainButton>
					</Col>
				</Row>

				{/* mobile view */}
				<Row className="mb-4 d-lg-none">
					<Col sm="12" md="12" className="mb-4">
						<HeadingText data-testid="editVideoPage">{translation.editVideoPage.shoppableVideo}</HeadingText>
					</Col>
					<Col sm="12" md="12" className="mb-4">
						{generateButton && !loading && (
							<MainSuccessButton
								type="button"
								data-testid="addAnotherVideo"
								style={{ width: "100%", marginBottom: "1rem" }}
								onClick={() => {
									if (hideCreateVideo) {
										setVideoModalStatus(true);
									} else {
										navigateToCreateVideo();
									}
								}}
							>
								{translation.editVideoPage.addAnotherVideo}
							</MainSuccessButton>
						)}

						<Row>
							<Col style={{ width: "50%" }} className="text-start">
								<DetailShareButton
									allowSharing={subscription?.allowSharing === true}
									disabled={false}
									setShareModalStatus={setShareModalStatus}
									shareType={ShareType.SHARE_VIDEO}
								/>
							</Col>
							<Col style={{ width: "50%" }} className="text-end">
								<MainButton
									type="button"
									data-testid="updateButton"
									inactive={generateButton}
									onClick={generateButton ? validateFields : handleSave}
									className="ms-3"
								>
									{translation.general.update}
									{loading ? (
										<Spinner as="span" size="sm" role="status" aria-hidden="true" style={{ marginLeft: "1rem" }} />
									) : (
										""
									)}
								</MainButton>
							</Col>
						</Row>
					</Col>
				</Row>

				{refetch ? (
					<Row pl="0" pr="0">
						<Col sm="12" md="3" className="mb-4">
							<Skeleton height={535} />
						</Col>
						<Col sm="12" md="4" className="mb-4">
							<Skeleton count={10} height={50} />
						</Col>
						<Col sm="12" md="5" className="mb-4">
							<Skeleton height={535} />
						</Col>
					</Row>
				) : (
					<Row pl="0" pr="0">
						<Col sm="12" md="3" className="mb-4 mt-2">
							<FlexSpace className="mb-3">
								<CustomSwitch
									type="switch"
									defaultChecked={displayShopIcon}
									onChange={() => setDisplayShopIcon((state) => !state)}
									label={translation.editVideoPage.displayShopIcon}
									id="displayShopIcon"
								/>
							</FlexSpace>

							<>
								<FileDropBox data-testid="fileDropDiv" style={{ cursor: "default" }}>
									{uploadFileURL && (
										<VideoWrapper>
											<VideoPreview
												subscription={subscription}
												appEndpoint={process.env.APP_ENDPOINT as string}
												title={videoTitleValue}
												videoURL={uploadFileURL}
												videoPosterURL={imagePreview}
												showTitle={displayTitle}
												emailAddress={emailAddress}
												phoneNumber={phoneNumber}
												products={productsInputs as VideoPropsProduct[]}
												videoDisplayMode={displayFormat}
												captionData={captionData}
											/>
										</VideoWrapper>
									)}
								</FileDropBox>
							</>
							{captionData && (
								<VideoCaptionsText
									data-testid="editCaptions"
									onClick={() => {
										setCaptionsModalStatus(true);
									}}
								>
									{translation.general.editCaptions}
								</VideoCaptionsText>
							)}
						</Col>
						<Col sm="12" md="4" className="mb-4">
							<FlexSpace>
								<VideoPageTitleText data-testid="videoTitleDiv" className="mb-3">
									{`${translation.editVideoPage.videoTitle}*`}{" "}
								</VideoPageTitleText>
								<div>
									<CustomSwitch
										type="switch"
										defaultChecked={displayTitle}
										onChange={() => setDisplayTitle((state) => !state)}
										label={translation.editVideoPage.displayTitle}
										id="displayTitleSwitch"
									/>
								</div>
							</FlexSpace>
							<Form.Group className="mb-3">
								<CustomInput
									className="form-control"
									type="text"
									required
									placeholder={translation.editVideoPage.enterTitle}
									id="videoTitle"
									data-testid="videoTitle"
									onChange={(value) => handleTitleChange(value.target.value)}
									value={videoTitleValue}
								/>
							</Form.Group>
							<FlexSpace>
								<VideoPageTitleText className="mb-3">
									{translation.editVideoPage.videoDescription}
								</VideoPageTitleText>
								<div>
									{videoDescriptionCount}/150
								</div>
							</FlexSpace>
							<Form.Group className="mb-3">
								<CustomTextarea
									className="form-control"
									maxLength={150}
									placeholder={translation.editVideoPage.videoDescPlaceholder}
									id="videoDescription"
									data-testid="videoDescription"
									onChange={(value) => handleDescChange(value.target.value)}
									value={videoDescValue}
								/>
							</Form.Group>

							<FlexSpace>
								<VideoPageTitleText data-testid="coverImageTitle" className="mb-2">
									{translation.editVideoPage.displayFormat}
								</VideoPageTitleText>
								<div>
									<CustomSwitch
										type="switch"
										defaultChecked={captionData?.enabled}
										onChange={toggleCaptionsEnabled}
										label={translation.editVideoPage.displayCaptions}
										id="displayCaptions"
									/>
								</div>
							</FlexSpace>

							{subscription?.allowLandscape ? (
								<FlexSwitchRow>
									<FlexSwitchCol
										active={displayFormat === DisplayFormatOptions.PORTRAIT}
										onClick={() => setDisplayFormat(DisplayFormatOptions.PORTRAIT)}
									>
										<PortraitBox />
										{translation.editVideoPage.portrait}
									</FlexSwitchCol>
									<FlexSwitchCol
										active={displayFormat === DisplayFormatOptions.LANDSCAPE}
										onClick={() => setDisplayFormat(DisplayFormatOptions.LANDSCAPE)}
									>
										<LandscapeBox />
										{translation.editVideoPage.landscape}
									</FlexSwitchCol>
								</FlexSwitchRow>
							) : (
								<FlexSwitchRow>
									<FlexSwitchCol active={true}>
										<PortraitBox />
										{translation.editVideoPage.portrait}
									</FlexSwitchCol>
									<FlexSwitchCol onClick={() => handlePlanClick()}>
										<LandscapeBox />
										{translation.editVideoPage.landscape}
										<img src={OpenLockIcon} style={{ marginLeft: "10px" }} />
									</FlexSwitchCol>
								</FlexSwitchRow>
							)}
							<VideoPageTitleText
								data-testid="coverImageTitle"
								className="mb-3 mt-4"
							>{`${translation.editVideoPage.coverImageText}*`}</VideoPageTitleText>
							<Form.Group className="mb-3">
								<div
									data-testid="coverImageDiv"
									onClick={() => {
										setCoverImageStatus(true);
									}}
								>
									<NoImageFileBox data-testid="coverImage">
										<div style={{ width: "75px" }}>
											<CoverImageBox
												landscape={displayFormat === DisplayFormatOptions.LANDSCAPE}
												data-testid="CoverImageBox"
											>
												{imagePreview ? (
													<UploadImage data-testid="uploadedCoverImage" src={imagePreview} />
												) : (
													<ImageIcon
														data-testid="CoverImageIcon"
														landscape={displayFormat === DisplayFormatOptions.LANDSCAPE || undefined}
													/>
												)}
											</CoverImageBox>
										</div>
										<div>
											<BodyText data-testid="uploadCoverImage" className="mt-3">
												{translation.editVideoPage.replaceCoverImage}
											</BodyText>
										</div>
									</NoImageFileBox>
								</div>
							</Form.Group>
							<div>&nbsp;</div>
							<VideoPageTitleText className="mb-2">{`${translation.editVideoPage.addInteraction}`}</VideoPageTitleText>

							{productsInputs.map((x, index) => {
								return (
									<Form.Group className="mb-3" key={index}>
										<ProductsBox>
											<CustomInputBox style={{ marginBottom: `${productsInputs[index].productURL ? "0.5rem" : "0"}` }}>
												<CustomInput
													className="form-control"
													type="text"
													required
													style={{ backgroundColor: "#ffffff" }}
													onChange={(e) => handleInputChange(e.target.value, "productURL", index)}
													onBlur={(e) => {
														if (!productsInputs[index].showProduct && !productsInputs[index].fetchingProduct) {
															handleParseProductUrl(e.target.value, index);
														}
													}}
													onKeyDown={(e) => {
														if (e.key === "Enter") {
															handleParseProductUrl((e.target as HTMLInputElement).value, index);
														}
													}}
													placeholder={translation.editVideoPage.productURL}
													id={`productURL${index}`}
													data-testid={`productURL${index}`}
													value={x.productURL}
												/>
												<CircleXmarkIcon
													style={{ position: "relative", top: "-4.3rem" }}
													data-testid={`deleteProductIcon${index}`}
													onClick={() => {
														handleRemoveClick(index);
													}}
												/>
											</CustomInputBox>
											{productsInputs[index].showProduct && productsInputs[index].fetchingProduct ? (
												<FetchingProductText>{translation.editVideoPage.fetchingProductText}</FetchingProductText>
											) : (
												productsInputs[index].showProduct &&
												!productsInputs[index].fetchingProduct && (
													<CustomInputBox style={{ display: "flex" }}>
														<ProductImage
															key={index}
															data-testid={`prImage${index}`}
															htmlFor={`prImage${index}`}
															onChange={(e: React.FormEvent<HTMLLabelElement>) => onDrop(e, index)}
															onMouseOver={() => handleMouseOver(index)}
															onMouseLeave={() => handleMouseLeave(index)}
														>
															<input
																type="file"
																id={`prImage${index}`}
																accept="image/png, image/jpeg, image/jpg"
																hidden
															/>
															{!x.hovering && x.imagePreview ? (
																<UploadProductImage key={"UploadProductImage" + index} src={x.imagePreview} />
															) : (
																<>
																	<img
																		src={UploadIcon}
																		alt={translation.editVideoPage.uploadImageAltText}
																		data-testid={`addImageIcon${index}`}
																		style={{ margin: "16px", width: "25px", height: "25px" }}
																	/>
																</>
															)}
														</ProductImage>
														<CustomInput
															className="form-control"
															type="text"
															required
															style={{ backgroundColor: "#ffffff", marginLeft: "0.5rem" }}
															onChange={(value) => handleInputChange(value.target.value, "productTitle", index)}
															placeholder={translation.general.title}
															id={`productTitle${index}`}
															data-testid={`productTitle${index}`}
															value={x.productTitle}
														/>
														<CustomInput
															className="form-control"
															type="text"
															style={{ backgroundColor: "#ffffff", marginLeft: "0.5rem" }}
															onChange={(value) => handleInputChange(value.target.value, "subTitle", index)}
															placeholder={translation.editVideoPage.subTitle}
															id={`subTitle${index}`}
															data-testid={`subTitle${index}`}
															value={x.subTitle}
														/>
													</CustomInputBox>
												)
											)}
										</ProductsBox>
									</Form.Group>
								);
							})}

							{showEmailAddress && subscription?.allowCTALead && (
								<Form.Group className="mb-3">
									<ProductsBox>
										<CustomInputBox>
											<CustomInput
												type="email"
												ref={emailRef}
												className="form-control"
												style={{ backgroundColor: "#ffffff" }}
												onChange={(e) => {
													setEmailAddress(e.target.value);
													if (emailRef.current?.classList.contains("is-invalid")) {
														emailRef.current.classList.remove("is-invalid");
													}
												}}
												placeholder={translation.editVideoPage.emailAddress}
												id="productEmailAddress"
												data-testid="productEmailAddress"
												value={emailAddress}
											/>
											<CircleXmarkIcon
												style={{ position: "relative", top: "-4.3rem" }}
												data-testid="deleteproductEmailAddress"
												onClick={() => {
													setEmailAddress("");
													setShowEmailAddress(false);
												}}
											/>
										</CustomInputBox>
									</ProductsBox>
								</Form.Group>
							)}

							{showPhoneNumber && subscription?.allowCTALead && (
								<Form.Group className="mb-3">
									<ProductsBox>
										<CustomInputBox>
											<CustomInput
												type="text"
												ref={phoneRef}
												className="form-control"
												style={{ backgroundColor: "#ffffff" }}
												onChange={(e) => setPhoneNumber(e.target.value)}
												placeholder={translation.editVideoPage.phoneNumber}
												id="productPhoneNumber"
												data-testid="productPhoneNumber"
												value={phoneNumber}
											/>
											<CircleXmarkIcon
												style={{ position: "relative", top: "-4.3rem" }}
												data-testid="deleteProductPhoneNumber"
												onClick={() => {
													setPhoneNumber("");
													setShowPhoneNumber(false);
												}}
											/>
										</CustomInputBox>
									</ProductsBox>
								</Form.Group>
							)}

							<Flex>
								{productsInputs.length < 10 && (
									<ImageButton onClick={() => handleAddClick()}>
										<img src={LinkIconBlack} />
									</ImageButton>
								)}
								{!showEmailAddress && (
									<ImageButton
										disabled={!subscription?.allowCTALead}
										onClick={() => {
											if (subscription?.allowCTALead) {
												if (!emailAddress && userData?.email) {
													setEmailAddress(userData.email);
												}
												setShowEmailAddress(true);
											} else {
												handlePlanClick();
											}
										}}
									>
										<img src={EmailIconBlack} />
										{!subscription?.allowCTALead && <img src={OpenLockIcon} className="lockImage" />}
									</ImageButton>
								)}
								{!showPhoneNumber && (
									<ImageButton
										disabled={!subscription?.allowCTALead}
										onClick={() => (subscription?.allowCTALead ? setShowPhoneNumber(true) : handlePlanClick())}
									>
										<img src={PhoneIconBlack} />
										{!subscription?.allowCTALead && <img src={OpenLockIcon} className="lockImage" />}
									</ImageButton>
								)}
							</Flex>
						</Col>
						<Col sm="12" md="5" className="mb-4" style={{ position: "relative" }}>
							<>
								<VideoPageTitleText data-testid="performance" className="mb-3">
									{translation.editVideoPage.performanceSubheading}
								</VideoPageTitleText>
								<VideoPerformance>
									{!videoData?.shoppableVideo?.phonePressCount &&
										!videoData?.shoppableVideo?.emailSubmitCount &&
										!videoData?.shoppableVideo.videoPlayCount &&
										(videoData?.shoppableVideo.linkClicks?.length === 0 ||
											videoData?.shoppableVideo.linkClicks === undefined) ? (
											<div style={{ textAlign: "center", fontWeight: "bold", paddingTop: "5rem", paddingBottom: "5rem" }}>
												{translation.editVideoPage.performanceNoData}
											</div>
										) : (
											<>
												<VideoPerformanceSection>
													<FlexCol>
														<VideoMetricTitle>{translation.editVideoPage.playsHeading}</VideoMetricTitle>
														<Flex>
															<img src={Plays} alt={translation.editVideoPage.playsHeading} data-testid="PlaysIcon" />
															<div className="ms-1">
																{videoData?.shoppableVideo.videoPlayCount !== undefined &&
																	videoData.shoppableVideo.videoPlayCount}
															</div>
														</Flex>
													</FlexCol>
													<FlexCol>
														<VideoMetricTitle>{translation.editVideoPage.clicksHeading}</VideoMetricTitle>
														<Flex>
															<img src={Clicks} alt={translation.editVideoPage.clicksHeading} data-testid="ClicksIcon" />
															<div className="ms-1">
																{videoData?.shoppableVideo.linkClicks !== undefined &&
																	videoData.shoppableVideo.linkClicks?.reduce(
																		(count, link) => count + link.clickCount,
																		0
																	)}
															</div>
														</Flex>
													</FlexCol>
													<FlexCol>
														<VideoMetricTitle>{translation.editVideoPage.likesHeading}</VideoMetricTitle>
														{subscription?.type !== ProductTypeEnum.BASIC ? (
															<Flex>
																<img src={Likes} alt={translation.editVideoPage.likesHeading} data-testid="LikesIcon" />
																<div className="ms-1">
																	{videoData?.shoppableVideo.likes !== undefined && videoData.shoppableVideo.likes}
																</div>
															</Flex>
														) : (
															<Flex>
																<BlurredLock onClick={() => navigate("/plans-pricing")}>
																	<img src={OpenLockIcon} />
																</BlurredLock>
															</Flex>
														)}
													</FlexCol>
													<FlexCol>
														<VideoMetricTitle>{translation.editVideoPage.playtimeHeading}</VideoMetricTitle>
														{videoData?.shoppableVideo.videoPlayDurationSeconds !== undefined &&
															secondsToFormattedTime(Math.round(videoData.shoppableVideo.videoPlayDurationSeconds))}
													</FlexCol>
												</VideoPerformanceSection>
												<VideoPerformanceSection style={{ marginTop: "15px", display: "block" }}>
													<VideoMetricTitle className="mb-1">
														{translation.editVideoPage.linksClicksHeading}
													</VideoMetricTitle>
													{videoData?.shoppableVideo?.phonePressCount !== undefined && subscription?.allowCTALead && (
														<FlexCol className="mb-1">
															<Flex>
																<div className="me-1" style={{ fontWeight: "700" }}>
																	{interactionsCount++}
																</div>
																<div className="ms-1">{translation.editVideoPage.phoneCalls}</div>
																<Flex style={{ marginLeft: "auto", height: "fit-content" }}>
																	<img
																		src={PhoneIconBlack}
																		alt={translation.editVideoPage.phoneCalls}
																		data-testid="PhoneIconBlackIcon"
																		style={{ width: "1rem" }}
																	/>
																	<div className="ms-2" style={{ textAlign: "left", width: "3rem" }}>
																		{videoData.shoppableVideo.phonePressCount}
																	</div>
																</Flex>
															</Flex>
														</FlexCol>
													)}
													{videoData?.shoppableVideo?.emailSubmitCount !== undefined && subscription?.allowCTALead && (
														<FlexCol className="mb-1">
															<Flex>
																<div className="me-1" style={{ fontWeight: "700" }}>
																	{interactionsCount++}
																</div>
																<div className="ms-1">{translation.editVideoPage.emailLeads}</div>
																<Flex style={{ marginLeft: "auto", height: "fit-content" }}>
																	<img
																		src={EmailIconBlack}
																		alt={translation.editVideoPage.emailLeads}
																		data-testid="EmailIconBlackIcon"
																		style={{ width: "1rem" }}
																	/>
																	<div className="ms-2" style={{ textAlign: "left", width: "3rem" }}>
																		{videoData.shoppableVideo.emailSubmitCount}
																	</div>
																</Flex>
															</Flex>
														</FlexCol>
													)}
													{videoData?.shoppableVideo.linkClicks !== undefined &&
														[...videoData.shoppableVideo.linkClicks]
															.sort((a, b) => {
																if (a.clickCount == null) return 1;
																if (b.clickCount == null) return -1;
																return b.clickCount - a.clickCount;
															})
															.map((link, index) => {
																return (
																	<FlexCol key={index} className="mb-1">
																		<Flex>
																			<div className="me-1" style={{ fontWeight: "700" }}>
																				{interactionsCount++}
																			</div>
																			<div className="ms-1">{link.productTitle}</div>
																			<Flex style={{ marginLeft: "auto", height: "fit-content" }}>
																				<img
																					src={Clicks}
																					alt={translation.editVideoPage.clicksHeading}
																					data-testid="ClicksIcon"
																					style={{ width: "1rem" }}
																				/>
																				<div className="ms-2" style={{ textAlign: "left", width: "3rem" }}>
																					{link.clickCount}
																				</div>
																			</Flex>
																		</Flex>
																	</FlexCol>
																);
															})}
												</VideoPerformanceSection>
												{subscription?.enableEngagementMetrics ? (
													<VideoPerformanceSection style={{ marginTop: "15px", display: "block" }}>
														<Flex>
															<VideoMetricTitle className="mb-3">
																{translation.editVideoPage.engagementScoreHeading}
															</VideoMetricTitle>
															<div className="ms-2" style={{ width: "1rem", marginTop: "-0.1rem", position: "relative" }}>
																<img
																	onMouseOver={() => setDisplayEngagementScoreInfo(true)}
																	onMouseLeave={() => setDisplayEngagementScoreInfo(false)}
																	src={HelpIconGrey}
																	alt={translation.editVideoPage.helpIconAlt}
																	data-testid="HelpIcon"
																/>
																{displayEngagementScoreInfo && (
																	<InfoBox>{translation.editVideoPage.engagementScoreDesc}</InfoBox>
																)}
															</div>
														</Flex>
														{videoData?.shoppableVideo.videoScore !== undefined && (
															<Scorebar score={Math.round(videoData.shoppableVideo.videoScore)} />
														)}
														<BarGraph
															percent20={videoData?.shoppableVideo.playPercentCount20 ?? 0}
															percent40={videoData?.shoppableVideo.playPercentCount40 ?? 0}
															percent60={videoData?.shoppableVideo.playPercentCount60 ?? 0}
															percent80={videoData?.shoppableVideo.playPercentCount80 ?? 0}
															percent100={videoData?.shoppableVideo.playPercentCount100 ?? 0}
														/>
														<VideoPercentageSubheading>
															{translation.editVideoPage.percentageVideoPlayed}
														</VideoPercentageSubheading>
													</VideoPerformanceSection>
												) : (
													<VideoPerformanceSection
														style={{ marginTop: "15px", display: "flex", position: "relative", flexDirection: "column" }}
													>
														<Flex>
															<VideoMetricTitle className="mb-3">
																{translation.editVideoPage.engagementScoreHeading}
															</VideoMetricTitle>
														</Flex>
														<img
															style={{ width: "100%", objectFit: "cover", margin: "auto", maxWidth: "34rem" }}
															src={EngagementScore}
															data-testid="engagement-score"
														/>
														<MainButton
															resize={true}
															style={{
																position: "absolute",
																left: "50%",
																top: "50%",
																transform: "translate(-50%, -50%)",
																width: "max-content"
															}}
															onClick={() => navigate("/plans-pricing")}
														>
															<StarIcon /> {translation.general.unlockPro}
														</MainButton>
													</VideoPerformanceSection>
												)}
											</>
										)}
								</VideoPerformance>
								<div>&nbsp;</div>
							</>
							<VideoPageTitleText data-testid="addToCollection" className="mb-3">
								{translation.editVideoPage.collectionsSubheading}
							</VideoPageTitleText>
							<ProductsBox>
								<CustomInputBox>
									<Select
										styles={CustomSelectStyles}
										options={unselectedCollections}
										onChange={handleCollectionSelect}
										components={{
											SingleValue: CustomSingleValue,
											Placeholder: CustomSingleValue,
											DropdownIndicator: () => null
										}}
									/>
								</CustomInputBox>
							</ProductsBox>
							<CustomSelectItem
								_id={defaultCollection?._id ?? ""}
								readonly={true}
								title={`${translation.editVideoPage.allVideosText} (${defaultCollection?.title})`}
								subtext={`${defaultCollection?.shoppableVideos?.length} ${translation.editVideoPage.videosText}`}
							/>
							{selectedCollections.map((collection) => {
								return (
									<CustomSelectItem
										key={collection._id}
										onRemove={handleCollectionRemove}
										_id={collection._id}
										title={`${collection?.title}`}
										subtext={`${collection?.shoppableVideos?.length} ${translation.editVideoPage.videosText}`}
									/>
								);
							})}
							{newCollectionSelected && (
								<CustomSelectItem
									onRemove={handleNewCollectionRemove}
									_id="new_collection"
									title={translation.createVideoPage.newCollectionText}
									subtext={`0 ${translation.createVideoPage.videosText}`}
								/>
							)}
						</Col>
					</Row>
				)}
			</PageBody>

			<CoverImageModal
				displayFormat={displayFormat}
				visible={coverImageStatus}
				uploadFileURL={uploadFileURL}
				videoDataCopy={videoDataCopy}
				imagePreview={imagePreview}
				setVideoDataCopy={setVideoDataCopy}
				setCoverImage={setCoverImage}
				setCoverImageURL={setCoverImageURL}
				setImagePreview={setImagePreview}
				setEditVideoError={setEditVideoError}
				onCancel={() => setCoverImageStatus(false)}
			/>
			<CaptionsModal
				displayFormat={displayFormat}
				visible={captionsModalStatus}
				uploadFileURL={uploadFileURL}
				captionData={captionData}
				setCaptionData={setCaptionData}
				videoDataCopy={videoDataCopy}
				setVideoDataCopy={setVideoDataCopy}
				setEditVideoError={setEditVideoError}
				onCancel={() => setCaptionsModalStatus(false)}
			/>
			<ConfirmLeavingModal
				visible={modalStatus}
				onCancel={() => {
					setModalStatus(false);
					setGoToPrevPage(false);
				}}
				onContinue={() => {
					if (goToPrevPage) window.history.go(-2);
					else navigate(navigationUrl);
				}}
			/>
			<EnterPasswordModal
				visible={enterPassword}
				onCancel={() => setEnterPassword(false)}
				onContinue={updateShoppableVideo}
			/>
			<VideoUsageModal visible={videoModalStatus} onCancel={() => setVideoModalStatus(false)} />
			{shareObject && (
				<ShareModal
					allowSharing={subscription?.allowSharing === true}
					visible={shareModalStatus}
					onCancel={() => setShareModalStatus(false)}
					shareObject={shareObject}
					saveChanges={saveChanges}
					setModalStatus={setModalStatus}
					setNavigationUrl={setNavigationUrl}
				/>
			)}
		</>
	);
};

export default EditVideoDetails;
