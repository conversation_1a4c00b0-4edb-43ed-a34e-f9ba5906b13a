import React from "react";
import { ButtonIcon, MainButton } from "../../styles/components";
import { LockIconBlack, ShareIconBlack } from "../../assets";
import { useTranslation } from "../hooks/translations";
import { useNavigate } from "react-router-dom";
import { ShareType } from "@src/types/share";

interface DetailShareButtonProps {
	allowSharing: boolean;
	disabled: boolean;
	setShareModalStatus: (status: boolean) => void;
	shareType?: ShareType;
	saveChanges?: boolean;
	setModalStatus?: (value: boolean) => void;
	setNavigationUrl?: (value: string) => void;
}

const DetailShareButton: React.FC<DetailShareButtonProps> = ({
	allowSharing,
	disabled,
	setShareModalStatus,
	shareType,
	saveChanges,
	setModalStatus,
	setNavigationUrl
}) => {
	const navigate = useNavigate();
	const translation = useTranslation();

	const handleClick = () => {
		if (allowSharing === true || shareType === ShareType.SHARE_VIDEO) {
			setShareModalStatus(true);
		} else {
			if (saveChanges && setNavigationUrl && setModalStatus) {
				setNavigationUrl("/plans-pricing");
				setModalStatus(true);
			} else {
				navigate("/plans-pricing");
			}
		}
	};

	return (
		<MainButton
			type="button"
			data-testid="ShareButton"
			style={{ marginRight: "1rem", marginLeft: "auto" }}
			onClick={handleClick}
			greyColor={true}
			disabled={disabled}
		>
			<ButtonIcon
				src={allowSharing === true || shareType === ShareType.SHARE_VIDEO ? ShareIconBlack : LockIconBlack}
				alt={translation.modals.copyLink}
			/>
			{translation.general.share}
		</MainButton>
	);
};

export default DetailShareButton;
